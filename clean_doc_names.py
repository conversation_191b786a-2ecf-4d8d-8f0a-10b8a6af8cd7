#!/usr/bin/env python3
"""
任务3：清洗数据库的bid_doc_name和contract_name字段内容

逻辑：
1. 如果bid_doc_link_key能匹配appendix_info下面某一dict的file_link_key，
   则以同一dict的text作为对应bid_doc_link_key的bid_doc_name
2. 如果contract_link_key能匹配appendix_info下面某一dict的file_link_key，
   则以同一dict的text作为对应contract_link_key的contract_name
3. 否则，把bid_doc_name、bid_doc_ext、bid_doc_link_out、bid_doc_link_key四个字段有值的bid_doc_name字段赋值"招标文件"
4. 把contract_name、contract_ext、contract_link_out、contract_link_key四个字段有值的contract_name字段赋值"合同文件"
"""

import argparse
import json
from datetime import datetime
from elasticsearch import Elasticsearch
from utils.log_cfg import log


def connect_elasticsearch(host="***********", port=9200):
    """连接Elasticsearch"""
    try:
        es = Elasticsearch([{"host": host, "port": port}])
        if es.ping():
            log.info(f"成功连接到Elasticsearch: {host}:{port}")
            return es
        else:
            log.error(f"无法连接到Elasticsearch: {host}:{port}")
            return None
    except Exception as e:
        log.error(f"连接Elasticsearch失败: {e}")
        return None


def get_documents_to_clean(es, index_name, batch_size=100):
    """获取需要清洗的文档"""
    query = {
        "query": {
            "bool": {
                "should": [
                    {"exists": {"field": "bid_doc_link_key"}},
                    {"exists": {"field": "contract_link_key"}}
                ],
                "minimum_should_match": 1
            }
        },
        "size": batch_size,
        "_source": [
            "bid_doc_name", "bid_doc_ext", "bid_doc_link_out", "bid_doc_link_key",
            "contract_name", "contract_ext", "contract_link_out", "contract_link_key",
            "appendix_info"
        ]
    }
    
    try:
        response = es.search(index=index_name, body=query, scroll="5m")
        scroll_id = response["_scroll_id"]
        hits = response["hits"]["hits"]
        
        while hits:
            for hit in hits:
                yield hit
            
            # 获取下一批
            response = es.scroll(scroll_id=scroll_id, scroll="5m")
            hits = response["hits"]["hits"]
            
    except Exception as e:
        log.error(f"获取文档失败: {e}")


def clean_doc_name_by_appendix_info(doc_data):
    """根据appendix_info清洗文档名称"""
    source = doc_data.get("_source", {})
    appendix_info = source.get("appendix_info", [])
    
    # 创建file_link_key到text的映射
    link_key_to_text = {}
    if isinstance(appendix_info, list):
        for item in appendix_info:
            if isinstance(item, dict):
                file_link_key = item.get("file_link_key")
                text = item.get("text")
                if file_link_key and text:
                    link_key_to_text[file_link_key] = text
    
    updates = {}
    
    # 处理bid_doc_name
    bid_doc_link_key = source.get("bid_doc_link_key")
    bid_doc_name = source.get("bid_doc_name")
    bid_doc_ext = source.get("bid_doc_ext")
    bid_doc_link_out = source.get("bid_doc_link_out")
    
    if bid_doc_link_key:
        if bid_doc_link_key in link_key_to_text:
            # 情况1：能匹配到appendix_info中的text
            new_bid_doc_name = link_key_to_text[bid_doc_link_key]
            if new_bid_doc_name != bid_doc_name:
                updates["bid_doc_name"] = new_bid_doc_name
                log.info(f"更新bid_doc_name: '{bid_doc_name}' -> '{new_bid_doc_name}'")
        else:
            # 情况2：无法匹配，但四个字段都有值，设置为"招标文件"
            if all([bid_doc_name, bid_doc_ext, bid_doc_link_out, bid_doc_link_key]):
                if bid_doc_name != "招标文件":
                    updates["bid_doc_name"] = "招标文件"
                    log.info(f"更新bid_doc_name为默认值: '{bid_doc_name}' -> '招标文件'")
    
    # 处理contract_name
    contract_link_key = source.get("contract_link_key")
    contract_name = source.get("contract_name")
    contract_ext = source.get("contract_ext")
    contract_link_out = source.get("contract_link_out")
    
    if contract_link_key:
        if contract_link_key in link_key_to_text:
            # 情况1：能匹配到appendix_info中的text
            new_contract_name = link_key_to_text[contract_link_key]
            if new_contract_name != contract_name:
                updates["contract_name"] = new_contract_name
                log.info(f"更新contract_name: '{contract_name}' -> '{new_contract_name}'")
        else:
            # 情况2：无法匹配，但四个字段都有值，设置为"合同文件"
            if all([contract_name, contract_ext, contract_link_out, contract_link_key]):
                if contract_name != "合同文件":
                    updates["contract_name"] = "合同文件"
                    log.info(f"更新contract_name为默认值: '{contract_name}' -> '合同文件'")
    
    return updates


def update_document(es, index_name, doc_id, updates, dry_run=False):
    """更新文档"""
    if not updates:
        return True
    
    if dry_run:
        log.info(f"[DRY RUN] 将更新文档 {doc_id}: {updates}")
        return True
    
    try:
        body = {"doc": updates}
        es.update(index=index_name, id=doc_id, body=body)
        log.info(f"成功更新文档 {doc_id}")
        return True
    except Exception as e:
        log.error(f"更新文档 {doc_id} 失败: {e}")
        return False


def clean_database(es, index_name, dry_run=False, max_docs=None):
    """清洗数据库"""
    log.info(f"开始清洗数据库，索引: {index_name}")
    log.info(f"干运行模式: {dry_run}")
    
    total_processed = 0
    total_updated = 0
    total_errors = 0
    
    for doc in get_documents_to_clean(es, index_name):
        if max_docs and total_processed >= max_docs:
            log.info(f"达到最大处理文档数限制: {max_docs}")
            break
        
        doc_id = doc["_id"]
        total_processed += 1
        
        try:
            updates = clean_doc_name_by_appendix_info(doc)
            
            if updates:
                success = update_document(es, index_name, doc_id, updates, dry_run)
                if success:
                    total_updated += 1
                else:
                    total_errors += 1
            
            if total_processed % 100 == 0:
                log.info(f"已处理 {total_processed} 个文档，更新 {total_updated} 个，错误 {total_errors} 个")
                
        except Exception as e:
            log.error(f"处理文档 {doc_id} 时发生异常: {e}")
            total_errors += 1
    
    log.info("=" * 60)
    log.info("数据清洗完成统计:")
    log.info(f"  总处理文档数: {total_processed}")
    log.info(f"  成功更新文档数: {total_updated}")
    log.info(f"  错误文档数: {total_errors}")
    log.info(f"  更新成功率: {(total_updated/total_processed*100):.1f}%" if total_processed > 0 else "0.0%")
    log.info("=" * 60)


def main():
    parser = argparse.ArgumentParser(description="清洗数据库的bid_doc_name和contract_name字段")
    parser.add_argument("--index", default="markersweb_attachment_analysis_alias", help="Elasticsearch索引名")
    parser.add_argument("--host", default="***********", help="Elasticsearch主机")
    parser.add_argument("--port", type=int, default=9200, help="Elasticsearch端口")
    parser.add_argument("--dry-run", action="store_true", help="干运行模式，不实际更新数据")
    parser.add_argument("--max-docs", type=int, help="最大处理文档数（用于测试）")
    
    args = parser.parse_args()
    
    # 连接Elasticsearch
    es = connect_elasticsearch(args.host, args.port)
    if not es:
        return
    
    # 执行清洗
    clean_database(es, args.index, args.dry_run, args.max_docs)


if __name__ == "__main__":
    main()
