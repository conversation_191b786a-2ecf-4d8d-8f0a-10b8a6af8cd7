2025-07-10 17:33:23.491 | DEBUG    | analyse_appendix:match_extracted_data_by_object_name:518 - object_name不匹配: '苯丙氨酸测定试剂盒' vs '新生儿促甲状腺激素测定试剂盒（时间分辨荧光免疫分析法）' (相似度: 0.512)
2025-07-10 17:33:23.492 | INFO     | analyse_appendix:match_extracted_data_by_object_name:513 - 找到匹配的object_name: '新生儿促甲状腺激素测定试剂盒' vs '新生儿促甲状腺激素测定试剂盒（时间分辨荧光免疫分析法）' (相似度: 1.000)
2025-07-10 17:33:23.494 | DEBUG    | analyse_appendix:match_extracted_data_by_object_name:518 - object_name不匹配: '完全不相关的产品A' vs '苯丙氨酸测定试剂盒（荧光分析法）' (相似度: 0.000)
2025-07-10 17:33:23.494 | DEBUG    | analyse_appendix:match_extracted_data_by_object_name:518 - object_name不匹配: '完全不相关的产品B' vs '苯丙氨酸测定试剂盒（荧光分析法）' (相似度: 0.000)
2025-07-10 17:33:23.494 | WARNING  | analyse_appendix:match_extracted_data_by_object_name:522 - 未找到匹配的object_name: '苯丙氨酸测定试剂盒（荧光分析法）'，跳过融合
2025-07-10 18:26:20.423 | INFO     | clean_doc_names:clean_doc_name_by_appendix_info:112 - 更新bid_doc_name: '旧的招标文件名' -> '广州医科大学附属第五医院部分后勤保障服务项目招标文件'
2025-07-10 18:26:20.423 | INFO     | clean_doc_names:clean_doc_name_by_appendix_info:136 - 更新contract_name: '旧的合同文件名' -> '服务合同协议书'
2025-07-10 18:26:20.424 | INFO     | clean_doc_names:clean_doc_name_by_appendix_info:118 - 更新bid_doc_name为默认值: '某个具体的文件名.docx' -> '招标文件'
2025-07-10 18:26:20.425 | INFO     | clean_doc_names:clean_doc_name_by_appendix_info:144 - 更新contract_name为默认值: '某个具体的合同名.pdf' -> '合同文件'
2025-07-10 18:26:20.426 | INFO     | clean_doc_names:clean_doc_name_by_appendix_info:112 - 更新bid_doc_name: '旧文件名' -> '新疆维吾尔自治区儿童医院招标文件'
2025-07-10 18:26:20.427 | INFO     | clean_doc_names:clean_doc_name_by_appendix_info:136 - 更新contract_name: '旧合同名' -> '广州医科大学服务合同'
2025-07-10 18:26:20.427 | INFO     | clean_doc_names:clean_doc_name_by_appendix_info:112 - 更新bid_doc_name: '旧文件名' -> '某医院设备采购招标文件'
2025-07-10 18:28:01.507 | ERROR    | __main__:connect_elasticsearch:32 - 连接Elasticsearch失败: NodeConfig.__init__() missing 1 required positional argument: 'scheme'
