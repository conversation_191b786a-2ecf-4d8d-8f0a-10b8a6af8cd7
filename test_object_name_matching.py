#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试object_name匹配验证和优化后的字段覆盖逻辑
"""

import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关函数
try:
    from analyse_appendix import (
        calculate_text_similarity,
        check_object_name_match,
        merge_analysis,
        is_valid_field_value,
    )

    print("✓ 成功导入object_name匹配相关函数")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)


def test_text_similarity():
    """测试文本相似度计算"""
    print("=" * 80)
    print("测试文本相似度计算")
    print("=" * 80)

    test_cases = [
        # (文本1, 文本2, 期望相似度范围, 描述)
        ("CT扫描仪", "CT扫描仪", 1.0, "完全相同"),
        ("CT扫描仪", "ct扫描仪", 1.0, "大小写不同"),
        ("CT扫描仪", " CT扫描仪 ", 1.0, "空格不同"),
        ("CT扫描仪", "CT扫描设备", (0.5, 0.6), "部分相似"),
        ("医用CT扫描仪", "CT扫描仪", (0.7, 0.8), "包含关系"),
        ("CT扫描仪", "MRI设备", (0.0, 0.1), "完全不同"),
        ("西门子CT扫描仪", "西门子CT扫描设备", (0.58, 0.6), "品牌+设备相似"),
        ("", "CT扫描仪", 0.0, "空字符串"),
        ("CT扫描仪", "", 0.0, "空字符串"),
        (None, "CT扫描仪", 0.0, "None值"),
    ]

    passed = 0
    for text1, text2, expected, description in test_cases:
        similarity = calculate_text_similarity(text1, text2)

        if isinstance(expected, tuple):
            is_valid = expected[0] <= similarity <= expected[1]
            expected_str = f"{expected[0]}-{expected[1]}"
        else:
            is_valid = abs(similarity - expected) < 0.01
            expected_str = str(expected)

        status = "✓" if is_valid else "✗"
        print(
            f"  {status} {description}: '{text1}' vs '{text2}' → {similarity} (期望: {expected_str})"
        )

        if is_valid:
            passed += 1

    print(f"\n文本相似度测试: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)


def test_object_name_matching():
    """测试object_name匹配验证"""
    print("\n" + "=" * 80)
    print("测试object_name匹配验证")
    print("=" * 80)

    test_cases = [
        # (主体object_name, 招标object_name, 期望匹配结果, 描述)
        (None, "CT扫描仪", True, "主体为空，直接使用招标文件"),
        ("", "CT扫描仪", True, "主体为空字符串"),
        ("CT扫描仪", None, False, "招标文件为空"),
        ("CT扫描仪", "CT扫描仪", True, "完全匹配"),
        ("CT扫描仪", "ct扫描仪", True, "大小写不同但匹配"),
        ("CT扫描仪", "CT扫描设备", False, "相似度不够，不匹配"),
        ("西门子CT扫描仪", "西门子CT扫描设备", False, "相似度不够，不匹配"),
        ("CT扫描仪", "MRI设备", False, "完全不同，不匹配"),
        ("医疗设备", "办公设备", False, "相似度低，不匹配"),
        ("超声诊断仪", "彩色多普勒超声诊断仪", True, "包含关系，应该匹配"),
    ]

    passed = 0
    for main_name, tender_name, expected_match, description in test_cases:
        is_match, similarity, reason = check_object_name_match(main_name, tender_name)

        status = "✓" if is_match == expected_match else "✗"
        print(f"  {status} {description}")
        print(f"      主体: '{main_name}' vs 招标: '{tender_name}'")
        print(f"      结果: 匹配={is_match}, 相似度={similarity}, 原因={reason}")

        if is_match == expected_match:
            passed += 1
        print()

    print(f"object_name匹配测试: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)


def test_conditional_tender_override():
    """测试有条件的招标文件覆盖"""
    print("\n" + "=" * 80)
    print("测试有条件的招标文件覆盖")
    print("=" * 80)

    # 测试用例1：主体object_name为空，应该直接覆盖
    print("\n1. 主体object_name为空，应该直接覆盖")
    main_result = {
        "bid_name": "第一标段",
        "object_name": None,  # 主体为空
        "object_brand": "主体品牌",
    }

    tender_result = {
        "object_name": "招标文件设备名称",
        "object_brand": "招标文件品牌",
        "object_model": "招标文件型号",
    }

    merged1 = merge_analysis(main_result, tender_result, {})

    checks1 = [
        ("object_name覆盖", merged1.get("object_name") == "招标文件设备名称"),
        ("object_brand覆盖", merged1.get("object_brand") == "招标文件品牌"),
        ("object_model新增", merged1.get("object_model") == "招标文件型号"),
    ]

    for desc, is_valid in checks1:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")

    # 测试用例2：object_name完全匹配，应该覆盖
    print("\n2. object_name完全匹配，应该覆盖")
    main_result2 = {
        "bid_name": "第一标段",
        "object_name": "CT扫描仪",
        "object_brand": "主体品牌",
    }

    tender_result2 = {
        "object_name": "CT扫描仪",  # 完全匹配
        "object_brand": "西门子",
        "object_conf": "详细技术参数",
    }

    merged2 = merge_analysis(main_result2, tender_result2, {})

    checks2 = [
        ("object_name保持", merged2.get("object_name") == "CT扫描仪"),
        ("object_brand覆盖", merged2.get("object_brand") == "西门子"),
        ("object_conf新增", merged2.get("object_conf") == "详细技术参数"),
    ]

    for desc, is_valid in checks2:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")

    # 测试用例3：object_name相似度高，应该覆盖
    print("\n3. object_name相似度高，应该覆盖")
    main_result3 = {
        "bid_name": "第一标段",
        "object_name": "CT扫描仪",
        "object_brand": "主体品牌",
    }

    tender_result3 = {
        "object_name": "CT扫描设备",  # 相似度高
        "object_brand": "飞利浦",
        "object_model": "型号123",
    }

    merged3 = merge_analysis(main_result3, tender_result3, {})

    # 检查相似度是否足够高来决定期望结果
    similarity = calculate_text_similarity("CT扫描仪", "CT扫描设备")
    should_override = similarity >= 0.6

    if should_override:
        checks3 = [
            ("object_name覆盖", merged3.get("object_name") == "CT扫描设备"),
            ("object_brand覆盖", merged3.get("object_brand") == "飞利浦"),
            ("object_model新增", merged3.get("object_model") == "型号123"),
        ]
    else:
        checks3 = [
            ("object_name不覆盖", merged3.get("object_name") == "CT扫描仪"),
            ("object_brand不覆盖", merged3.get("object_brand") == "主体品牌"),
            ("object_model不添加", merged3.get("object_model") is None),
        ]

    for desc, is_valid in checks3:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")

    # 测试用例4：object_name不匹配，不应该覆盖
    print("\n4. object_name不匹配，不应该覆盖")
    main_result4 = {
        "bid_name": "第一标段",
        "object_name": "CT扫描仪",
        "object_brand": "主体品牌",
    }

    tender_result4 = {
        "object_name": "MRI设备",  # 完全不同
        "object_brand": "招标品牌",
        "object_model": "招标型号",
    }

    merged4 = merge_analysis(main_result4, tender_result4, {})

    checks4 = [
        ("object_name不覆盖", merged4.get("object_name") == "CT扫描仪"),
        ("object_brand不覆盖", merged4.get("object_brand") == "主体品牌"),
        ("object_model不添加", merged4.get("object_model") is None),
    ]

    for desc, is_valid in checks4:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")

    return all(is_valid for _, is_valid in checks1 + checks2 + checks3 + checks4)


def main():
    """运行所有测试"""
    print("object_name匹配验证和优化后的字段覆盖逻辑测试")
    print("=" * 80)

    test_results = []

    try:
        # 运行各项测试
        test_results.append(test_text_similarity())
        test_results.append(test_object_name_matching())
        test_results.append(test_conditional_tender_override())

        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)

        test_names = ["文本相似度计算", "object_name匹配验证", "有条件招标文件覆盖"]

        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")

        passed_count = sum(test_results)
        total_count = len(test_results)

        print(f"\n总计: {passed_count}/{total_count} 个测试通过")

        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！object_name匹配验证功能正常！")
            print("\n功能特性:")
            print("✅ 文本相似度计算（编辑距离+Jaccard相似度）")
            print("✅ object_name匹配验证（阈值0.8）")
            print("✅ 有条件的招标文件字段覆盖")
            print("✅ 详细的匹配日志记录")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")

    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
