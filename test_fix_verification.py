#!/usr/bin/env python3
"""
测试修复后的文件处理逻辑
"""

def test_early_exit_logic():
    """测试早期退出逻辑"""
    print("=== 测试早期退出逻辑 ===")
    
    # 模拟文件列表
    files = [
        "广州医科大学附属第五医院部分后勤保障服务项目招标文件（2025070401）.docx",
        "广州医科大学附属第五医院部分后勤保障服务项目招标文件（2025070401）.pdf", 
        "第二章采购需求.docx",
        "广州市政府采购委托代理协议已盖章.pdf",
        "实质性响应一览表.docx"
    ]
    
    # 模拟关键词检测
    TENDER_KEYWORDS = ["招标文件", "磋商文件", "谈判文件", "采购需求"]
    CONTRACT_KEYWORDS = ["合同", "服务协议"]
    
    def mock_detect_file_type(filename):
        """模拟文件类型检测"""
        text = filename.lower()
        
        # 检查招标文件关键词
        for kw in TENDER_KEYWORDS:
            if kw in text:
                return "招标文件"
        
        # 检查合同文件关键词  
        for kw in CONTRACT_KEYWORDS:
            if kw in text:
                return "合同文件"
                
        return "其他"
    
    # 模拟修复后的逻辑
    found_tender_file = False
    found_contract_file = False
    processed_files = []
    skipped_files = []
    
    for filename in files:
        print(f"\n处理文件: {filename}")
        
        # 先进行文件类型检测
        file_type = mock_detect_file_type(filename)
        print(f"  文件类型: {file_type}")
        
        # 早期退出检查
        if file_type == "招标文件" and found_tender_file:
            print(f"  ✓ 早期退出：已找到招标文件，跳过: {filename}")
            skipped_files.append(filename)
            continue
        elif file_type == "合同文件" and found_contract_file:
            print(f"  ✓ 早期退出：已找到合同文件，跳过: {filename}")
            skipped_files.append(filename)
            continue
        elif file_type == "其他":
            print(f"  ✓ 跳过非招标/合同文件: {filename}")
            skipped_files.append(filename)
            continue
        
        # 立即设置标志
        if file_type == "招标文件":
            found_tender_file = True
            print(f"  ✓ 设置招标文件标志")
        elif file_type == "合同文件":
            found_contract_file = True
            print(f"  ✓ 设置合同文件标志")
        
        # 模拟文件处理
        print(f"  ✓ 处理文件: {filename}")
        processed_files.append(filename)
    
    print(f"\n=== 处理结果 ===")
    print(f"处理的文件 ({len(processed_files)}):")
    for f in processed_files:
        print(f"  - {f}")
    
    print(f"\n跳过的文件 ({len(skipped_files)}):")
    for f in skipped_files:
        print(f"  - {f}")
    
    # 验证结果
    expected_processed = [
        "广州医科大学附属第五医院部分后勤保障服务项目招标文件（2025070401）.docx"
    ]
    expected_skipped = [
        "广州医科大学附属第五医院部分后勤保障服务项目招标文件（2025070401）.pdf",
        "第二章采购需求.docx", 
        "广州市政府采购委托代理协议已盖章.pdf",
        "实质性响应一览表.docx"
    ]
    
    print(f"\n=== 验证结果 ===")
    if processed_files == expected_processed:
        print("✓ 处理的文件列表正确")
    else:
        print("✗ 处理的文件列表不正确")
        print(f"  期望: {expected_processed}")
        print(f"  实际: {processed_files}")
    
    if skipped_files == expected_skipped:
        print("✓ 跳过的文件列表正确")
    else:
        print("✗ 跳过的文件列表不正确")
        print(f"  期望: {expected_skipped}")
        print(f"  实际: {skipped_files}")

if __name__ == "__main__":
    test_early_exit_logic()
