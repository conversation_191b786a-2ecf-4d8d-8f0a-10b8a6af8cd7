import os
from io import BytesIO
from urllib.parse import urlparse
from typing import Optional, List, Dict
import tempfile
import subprocess
import shutil

import docx  # to add (from python-docx)

import pdfplumber  # 更可靠的PDF解析库

PDF_PARSER = "pdfplumber"

try:
    from markitdown import MarkItDown

    MARKITDOWN_AVAILABLE = True
except ImportError:
    MarkItDown = None
    MARKITDOWN_AVAILABLE = False
import requests  # to add
import patoolib  # to add
import filetype  # to add
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from es_deal import init_es_client, search_documents, insert_document
from dotenv import load_dotenv
from datetime import datetime
import json
from openai import OpenAI
from utils.log_cfg import log
from markdownify import markdownify as md
from file_upload_service import upload_document_file, upload_attachment_file
from blacklist_manager import BlacklistManager, print_blacklist_stats


# Module-level constants for keywords
# TENDER_KEYWORDS = [
#     "招标文件",
#     "采购文件",
#     "询比文件",
#     "比选文件",
#     "竞价文件",
#     "竞争性谈判文件",
#     "议标文件",
#     "询价文件",
#     "技术规范书",
#     "谈判文件",
#     "磋商文件",
#     "竞争性磋商邀请文件",
#     "单一来源采购文件",
#     "招标公告",
#     "采购公告",
#     "招标书",
#     "公开文件",
# ]
TENDER_KEYWORDS = ["招标文件", "磋商文件", "谈判文件", "采购文件"]
# 合同相关关键词，用于公告类型999的附件内容过滤
CONTRACT_KEYWORDS = ["合同", "服务协议", "采购协议", "供货合同"]


def create_robust_session() -> requests.Session:
    """
    创建一个具有重试机制和连接池的健壮会话

    Returns:
        配置好的requests.Session对象
    """
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=3,  # 总重试次数
        backoff_factor=1,  # 退避因子
        status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
        allowed_methods=["HEAD", "GET", "OPTIONS"],  # 允许重试的方法
    )

    # 配置HTTP适配器
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,  # 连接池大小
        pool_maxsize=20,  # 连接池最大连接数
        pool_block=False,  # 连接池满时不阻塞
    )

    # 为HTTP和HTTPS挂载适配器
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


CONTRACT_FIELDS = [
    "bidder_price",
    # "bidder_name",
    "bidder_contact_person",
    "bidder_contact_phone_number",
    "bidder_contract_config_param",
    "contract_name",
    "contract_ext",
    "contract_link_out",
    "contract_link_key",
]

# 招标文件优先覆盖字段 - 这些字段如果在招标文件中有有效值，则覆盖融合结果
TENDER_OVERRIDE_FIELDS = [
    "object_name",  # 标的物名称
    "object_brand",  # 标的物品牌
    "object_model",  # 标的物型号
    # "object_supplier",  # 标的物供应商
    "object_produce_area",  # 标的物产地
    "object_conf",  # 标的物配置参数
    "object_oem",  # 标的物OEM厂家
    "object_amount",  # 标的物数量
    "object_unit",  # 标的物单位
    "object_price",  # 标的物单价
    "object_total_price",  # 标的物总价
    "object_maintenance_period",  # 标的物维保期限
    "object_price_source",  # 标的物价格来源
    "object_quality",  # 标的物质量层次
]

# 合同文件优先覆盖字段 - 这些字段如果在合同文件中有有效值，则覆盖融合结果
CONTRACT_OVERRIDE_FIELDS = [
    "bidder_price",  # 中标金额
    "bidder_name",  # 中标单位名称
    "bidder_contact_person",  # 中标单位联系人
    "bidder_contact_phone_number",  # 中标单位联系人电话
    "bidder_contract_config_param",  # 中标合同配置参数
]

# 标准字段列表 - 所有插入ES的文档都应包含这些字段
STANDARD_FIELDS = (
    # 业务数据字段（40个）
    "bid_name",  # 标段名称
    "bid_number",  # 标段编号
    "bid_budget",  # 标段预算金额
    "fiscal_delegation_number",  # 财政委托编号
    "prj_addr",  # 招标项目地址
    "prj_name",  # 招标项目名称
    "prj_number",  # 招标项目编号
    "prj_type",  # 招标项目类型
    "release_time",  # 发布日期
    "prj_approval_authority",  # 项目审批单位
    "superintendent_office",  # 监督部门
    "superintendent_office_code",  # 监督部门编号
    "tenderee",  # 招标人
    "bid_submission_deadline",  # 投标截止时间
    "trade_platform",  # 交易平台
    "procurement_method",  # 采购方式
    "prj_sub_type",  # 项目细分类型
    "province",  # 省份
    "city",  # 城市
    "county",  # 区县
    "announcement_type",  # 公告类型
    "object_name",  # 标的物名称
    "object_brand",  # 标的物品牌
    "object_model",  # 标的物型号
    "object_supplier",  # 标的物供应商
    "object_produce_area",  # 标的物产地
    "object_conf",  # 标的物配置参数
    "object_oem",  # 标的物OEM厂家
    "object_amount",  # 标的物数量
    "object_unit",  # 标的物单位
    "object_price",  # 标的物单价
    "object_total_price",  # 标的物总价
    "object_maintenance_period",  # 标的物维保期限
    "object_price_source",  # 标的物价格来源
    "object_quality",  # 标的物质量层次
    "bidder_price",  # 中标金额
    "bidder_name",  # 中标单位名称
    "bidder_contact_person",  # 中标单位联系人
    "bidder_contact_phone_number",  # 中标单位联系人电话
    "bidder_contract_config_param",  # 中标合同配置参数
    "agent",  # 代理机构
    "service_fee",  # 代理服务收费金额
    "bid_cancelled_flag",  # 标段是否废标标记
    "bid_cancelled_reason",  # 标段废标原因
    # 源数据元数据字段（6个）
    "source_id",  # 源文档ID
    "source_title",  # 源文档标题
    "source_create_time",  # 源文档创建时间
    "source_category",  # 源文档分类
    "source_url",  # 源文档URL
    "source_appendix",  # 源文档附件信息
    "appendix_info",  # 所有附件文件信息（包含上传ID）
    # 附件相关字段（8个）
    "bid_doc_name",  # 招标文件名称
    "bid_doc_ext",  # 招标文件扩展名
    "bid_doc_link_out",  # 招标文件外部链接
    "bid_doc_link_key",  # 招标文件上传ID
    "contract_name",  # 合同文件名称
    "contract_ext",  # 合同文件扩展名
    "contract_link_out",  # 合同文件外部链接
    "contract_link_key",  # 合同文件上传ID
    # 系统字段（1个）
    "insert_time",  # 插入时间
)

# 招标公告(001)类型允许的字段列表
ANNOUNCEMENT_001_FIELDS = (
    # 业务数据字段
    "bid_name",  # 标段名称
    "bid_number",  # 标段编号
    "bid_budget",  # 标段预算金额
    "fiscal_delegation_number",  # 财政委托编号
    "prj_addr",  # 招标项目地址
    "prj_name",  # 招标项目名称
    "prj_number",  # 招标项目编号
    "prj_type",  # 招标项目类型
    "release_time",  # 发布日期
    "prj_approval_authority",  # 项目审批单位
    "superintendent_office",  # 监督部门
    "superintendent_office_code",  # 监督部门编号
    "tenderee",  # 招标人
    "bid_submission_deadline",  # 投标截止时间
    "trade_platform",  # 交易平台
    "procurement_method",  # 采购方式
    "prj_sub_type",  # 项目细分类型
    "province",  # 省份
    "city",  # 城市
    "county",  # 区县
    "announcement_type",  # 公告类型
    "object_name",  # 标的物名称
    "object_brand",  # 标的物品牌
    "object_model",  # 标的物型号
    "object_supplier",  # 标的物供应商
    "object_produce_area",  # 标的物产地
    "object_conf",  # 标的物配置参数
    "object_oem",  # 标的物OEM厂家
    "object_amount",  # 标的物数量
    "object_unit",  # 标的物单位
    "object_price",  # 标的物单价
    "object_total_price",  # 标的物总价
    "object_maintenance_period",  # 标的物维保期限
    "object_price_source",  # 标的物价格来源
    "object_quality",  # 标的物质量层次
    "agent",  # 代理机构
    "service_fee",  # 代理服务收费金额
    # 源数据元数据字段
    "source_id",  # 源文档ID
    "source_title",  # 源文档标题
    "source_create_time",  # 源文档创建时间
    "source_category",  # 源文档分类
    "source_url",  # 源文档URL
    "source_appendix",  # 源文档附件信息
    "appendix_info",  # 所有附件文件信息（包含上传ID）
    # 附件相关字段
    "bid_doc_name",  # 招标文件名称
    "bid_doc_ext",  # 招标文件扩展名
    "bid_doc_link_out",  # 招标文件外部链接
    "bid_doc_link_key",  # 招标文件上传ID
    # 系统字段
    "insert_time",  # 插入时间
)

# 定义所有可能的字段列表，用于智能检索
ALL_FIELDS = [
    "bid_name",
    "bid_number",
    "bid_budget",
    "fiscal_delegation_number",
    "prj_addr",
    "prj_name",
    "prj_number",
    "prj_type",
    "release_time",
    "prj_approval_authority",
    "superintendent_office",
    "superintendent_office_code",
    "tenderee",
    "bid_submission_deadline",
    "trade_platform",
    "procurement_method",
    "prj_sub_type",
    "province",
    "city",
    "county",
    "announcement_type",
    "object_name",
    "object_brand",
    "object_model",
    "object_supplier",
    "object_produce_area",
    "object_conf",
    "object_oem",
    "object_amount",
    "object_unit",
    "object_price",
    "object_total_price",
    "object_maintenance_period",
    "object_price_source",
    "object_quality",
    "bidder_price",
    "bidder_name",
    "bidder_contact_person",
    "bidder_contact_phone_number",
    "bidder_contract_config_param",
    "agent",
    "service_fee",
    "bid_cancelled_flag",
    "bid_cancelled_reason",
]


def identify_missing_fields(result_dict: dict) -> List[str]:
    """
    识别字典中的空缺字段

    Args:
        result_dict: 解析结果字典

    Returns:
        List[str]: 空缺字段名列表
    """
    missing_fields = []

    for field in ALL_FIELDS:
        value = result_dict.get(field)
        # 检查字段是否为空缺（None、空字符串、空白字符串）
        if value is None or (isinstance(value, str) and not value.strip()):
            missing_fields.append(field)

    return missing_fields


def extract_fields_from_content(
    content: str,
    missing_fields: List[str],
    model_apikey: str,
    model_name: str,
    model_url: str,
    timeout: int = 300,
    max_retries: int = 2,
) -> dict:
    """
    从文档内容中提取指定字段的值

    Args:
        content: 文档内容
        missing_fields: 需要提取的字段列表
        model_apikey: API密钥
        model_name: 模型名称
        model_url: 模型URL
        timeout: 超时时间
        max_retries: 最大重试次数

    Returns:
        dict: 提取到的字段值字典
    """
    if not missing_fields or not content.strip():
        return {}

    # 检查内容长度，避免过长内容导致超时
    max_content_length = 32768  # 最大32K字符（比analyze_content稍小）
    if len(content) > max_content_length:
        log.warning(
            f"智能融合文档内容过长({len(content)}字符)，截取前{max_content_length}字符"
        )
        content = content[:max_content_length] + "\n...(内容已截取)"

    # 构建字段描述字典
    field_descriptions = {
        "bid_name": "标段名称,标段有时也叫包组或者包,字段类型是text",
        "bid_number": "标段编号,标段有时也叫包组或者包,字段类型是keyword",
        "bid_budget": "标段预算金额,标段有时也叫包组或者包,字段类型是double",
        "fiscal_delegation_number": "财政委托编号,字段类型是keyword",
        "prj_addr": "招标项目地址,字段类型是text",
        "prj_name": "招标项目名称,字段类型是text",
        "prj_number": "招标项目编号,字段类型是keyword",
        "prj_type": "招标项目类型(工程,货物,服务),字段类型是keyword",
        "release_time": "发布日期,字段类型是date,字段格式是yyyy-MM-dd HH:mm:ss",
        "prj_approval_authority": "项目审批单位,字段类型是text",
        "superintendent_office": "监督部门,字段类型是text",
        "superintendent_office_code": "监督部门编号,字段类型是keyword",
        "tenderee": "招标人,字段类型是text",
        "bid_submission_deadline": "投标截止时间,字段类型是date,字段格式是yyyy-MM-dd HH:mm:ss",
        "trade_platform": "交易平台,字段类型是text",
        "procurement_method": "采购方式,字段类型是text",
        "prj_sub_type": "项目细分类型(设备,维保,耗材,试剂,手术器械,其他),字段类型是keyword",
        "province": "省份,必须是XX省,字段类型是keyword",
        "city": "城市,city必须是XX市,字段类型是keyword",
        "county": "区县,必须是XX区或者XX县,但不能是市辖区,字段类型是keyword",
        "announcement_type": "公告类型(001,002,003,004,010,999),001代表招标公告,002代表候选人公示,003代表更正/澄清公告,004代表结果公告,010代表中标通知书,999代表其他,字段类型是keyword",
        "object_name": "标的物名称,字段类型是text",
        "object_brand": "标的物品牌,字段类型是keyword",
        "object_model": "标的物型号,字段类型是keyword",
        "object_supplier": "标的物供应商,字段类型是text",
        "object_produce_area": "标的物产地,字段类型是text",
        "object_conf": "标的物配置参数,它是招标需求、采购需求、项目需求里的采购需求、技术要求、技术规格、技术参数、参数要求、配置要求、规格型号、货物明细、规格参数等内容,字段类型是text",
        "object_oem": "标的物OEM厂家,字段类型是text",
        "object_amount": "标的物数量,字段类型是integer",
        "object_unit": "标的物单位,字段类型是keyword",
        "object_price": "标的物单价,只有一个单价,不存在多个单价,字段类型是double",
        "object_total_price": "标的物总价,只有一个总价,不存在多个总价,不存在多个总价,如果对象存在标的物单价(object_price)和标的物数量(object_amount)且不为''、0和null的情况下,标的物总价(object_total_price)等于标的物单价(object_price)乘以标的物数量(object_amount),字段类型是double",
        "object_maintenance_period": "标的物维保期限,字段类型是keyword",
        "object_price_source": "标的物价格来源,字段类型是keyword",
        "object_quality": "标的物质量层次(1,2),1代表国产,2代表进口,字段类型是keyword",
        "bidder_price": "中标金额,字段类型是double",
        "bidder_name": "中标单位名称,字段类型是text",
        "bidder_contact_person": "中标单位联系人,字段类型是text",
        "bidder_contact_phone_number": "中标单位联系人电话,字段类型是keyword",
        "bidder_contract_config_param": "中标合同配置参数,它是中标合同或者服务协议里的报价表和技术应答,字段类型是text",
        "agent": "代理机构,字段类型是text",
        "service_fee": "代理服务收费金额,字段类型是double",
        "bid_cancelled_flag": "标段是否废标标记,废标填1,否则填null,标段有时也叫包组或者包,字段类型是keyword",
        "bid_cancelled_reason": "标段废标原因,标段有时也叫包组或者包,字段类型是text",
    }

    # 构建需要提取的字段描述
    fields_to_extract = {}
    for field in missing_fields:
        if field in field_descriptions:
            fields_to_extract[field] = field_descriptions[field]

    if not fields_to_extract:
        return {}

    # 构建提取prompt
    prompt = f"""
    你是招投标行业的专家，请从以下文档内容中提取指定的字段信息：

    文档内容：
    {content}

    请提取以下字段的值：
    {json.dumps(fields_to_extract, ensure_ascii=False, indent=2)}

    要求：
    - 使用中文回答
    - 以JSON格式返回结果，可直接用Python的json.loads解析
    - 只返回JSON对象，不要包含其他文本
    - 金额单位统一转换为人民币元
    - 下面字段类型是Elasticsearch数据库的数据类型
    - **如果公告涉及多个物品（如object_name、object_brand、object_model、object_supplier、object_price等字段），请将每个物品单独作为一个对象输出，最终返回一个对象列表（list of dict），每个对象包含该物品的所有相关字段。不要将多个物品的信息合并为一个字符串**
    - **list of dict的最外层必须是list，内部必须是dict，内部不要是list的类型**
    - 其他非物品相关字段（如项目名称、招标人等）需要放在每个对象中
    - 严格根据以下字典里字段的类型和描述要求提取公告中的键值对，未提取到的值赋值null，不确定的赋值null，只输出确定的结果，不要有多余注释和重复字段：
    """

    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": "请严格按照要求提取字段值"},
    ]

    try:
        result = llm(
            messages,
            model_name=model_name,
            model_apikey=model_apikey,
            model_url=model_url,
            timeout=timeout,
            max_retries=max_retries,
        )

        # 清理和解析JSON
        cleaned_result = clean_json_data(result)
        extracted_data = json.loads(cleaned_result)

        log.info(f"从文档内容中提取到字段: {list(extracted_data.keys())}")
        return extracted_data

    except Exception as e:
        log.error(f"从文档内容提取字段失败: {e}")
        return {}


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本的相似度（优化版本，特别适用于标的物名称匹配）

    Args:
        text1: 第一个文本
        text2: 第二个文本

    Returns:
        float: 相似度分数 (0.0-1.0)，1.0表示完全相同
    """
    if not text1 or not text2:
        return 0.0

    # 标准化文本：去除空格、转小写、去除常见无关词
    def normalize_text(text):
        text = str(text).strip().lower()
        # 去除常见的无关词汇
        remove_words = [
            "关于",
            "项目",
            "采购",
            "招标",
            "公告",
            "中标",
            "合同",
            "服务项目",
        ]
        for word in remove_words:
            text = text.replace(word, "")
        return text.strip()

    text1_norm = normalize_text(text1)
    text2_norm = normalize_text(text2)

    # 完全匹配
    if text1_norm == text2_norm:
        return 1.0

    # 计算编辑距离相似度
    def levenshtein_distance(s1: str, s2: str) -> int:
        """计算编辑距离"""
        if len(s1) < len(s2):
            return levenshtein_distance(s2, s1)

        if len(s2) == 0:
            return len(s1)

        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row

        return previous_row[-1]

    # 计算编辑距离相似度
    max_len = max(len(text1_norm), len(text2_norm))
    if max_len == 0:
        return 1.0

    edit_distance = levenshtein_distance(text1_norm, text2_norm)
    edit_similarity = 1.0 - (edit_distance / max_len)

    # 计算字符集合相似度（Jaccard相似度）
    set1 = set(text1_norm)
    set2 = set(text2_norm)
    intersection = len(set1 & set2)
    union = len(set1 | set2)
    jaccard_similarity = intersection / union if union > 0 else 0.0

    # 注意：原有的医疗设备词汇相似度计算已被新的核心词汇匹配替代

    # 检查包含关系（优化版本）
    containment_similarity = 0.0
    if text1_norm in text2_norm or text2_norm in text1_norm:
        shorter_len = min(len(text1_norm), len(text2_norm))
        longer_len = max(len(text1_norm), len(text2_norm))
        base_containment = shorter_len / longer_len if longer_len > 0 else 0.0

        # 如果短文本完全包含在长文本中，给予更高的相似度
        if shorter_len >= 3:  # 至少3个字符才有意义
            # 对于明显的包含关系，给予更高的基础分数
            if shorter_len >= 4:  # 4个字符以上的包含关系
                containment_similarity = 0.9
            else:
                containment_similarity = 0.8
        else:
            containment_similarity = base_containment

    # 检查核心词汇匹配（提取关键词进行匹配）
    def extract_core_words(text):
        """提取核心词汇，使用简单的中文分词"""
        import re

        # 简单的中文分词：基于常见的2-4字词汇模式
        words = set()

        # 提取2字词汇
        for i in range(len(text) - 1):
            word = text[i : i + 2]
            if re.match(r"^[\u4e00-\u9fff]{2}$", word):
                words.add(word)

        # 提取3字词汇
        for i in range(len(text) - 2):
            word = text[i : i + 3]
            if re.match(r"^[\u4e00-\u9fff]{3}$", word):
                words.add(word)

        # 提取4字词汇
        for i in range(len(text) - 3):
            word = text[i : i + 4]
            if re.match(r"^[\u4e00-\u9fff]{4}$", word):
                words.add(word)

        return words

    core_words1 = extract_core_words(text1_norm)
    core_words2 = extract_core_words(text2_norm)

    core_word_similarity = 0.0
    if core_words1 and core_words2:
        intersection = len(core_words1 & core_words2)
        union = len(core_words1 | core_words2)
        core_word_similarity = intersection / union if union > 0 else 0.0

        # 如果有核心词汇完全匹配，给予额外加分
        if intersection > 0:
            # 检查是否有长词汇匹配（更重要的匹配）
            long_word_match = any(
                len(word) >= 4 for word in (core_words1 & core_words2)
            )
            if long_word_match:
                core_word_similarity = min(core_word_similarity + 0.3, 1.0)
            elif intersection >= 2:  # 多个词汇匹配
                core_word_similarity = min(core_word_similarity + 0.2, 1.0)

    # 检查语义相关性（连续词汇组合匹配）
    semantic_similarity = 0.0
    if core_word_similarity > 0:
        # 多种语义相关性检查
        # 方式1: 检查是否有4字词汇组合匹配（如"清洁服务"）
        long_word_intersection = [
            word for word in (core_words1 & core_words2) if len(word) >= 4
        ]
        if long_word_intersection:
            semantic_similarity += 0.2  # 4字词汇匹配加分

        # 方式2: 检查核心概念匹配（多个相关词汇）
        if len(core_words1 & core_words2) >= 2:
            # 有多个核心词汇匹配，说明语义高度相关
            semantic_similarity += 0.15

        # 方式3: 检查服务类型匹配
        service_words = {"服务", "外包", "管理", "维护", "采购"}
        if (core_words1 & service_words) and (core_words2 & service_words):
            semantic_similarity += 0.1  # 服务类型相关加分

    # 动态权重分配：根据核心词汇匹配度调整权重
    # 当核心词汇匹配度高时，降低包含关系权重，提高核心词汇权重
    if core_word_similarity >= 0.6:
        # 高核心词汇匹配：包含关系30%，核心词汇50%，编辑距离15%，Jaccard5%
        combined_similarity = (
            containment_similarity * 0.3
            + core_word_similarity * 0.5
            + edit_similarity * 0.15
            + jaccard_similarity * 0.05
        )
    else:
        # 低核心词汇匹配：包含关系50%，核心词汇30%，编辑距离15%，Jaccard5%
        combined_similarity = (
            containment_similarity * 0.5
            + core_word_similarity * 0.3
            + edit_similarity * 0.15
            + jaccard_similarity * 0.05
        )

    # 应用语义相关性加分
    combined_similarity = min(combined_similarity + semantic_similarity, 1.0)

    return round(combined_similarity, 3)


def check_object_name_match(
    main_object_name, tender_object_name, threshold: float = 0.5
) -> tuple[bool, float, str]:
    """
    检查主体文件和招标文件的object_name是否匹配

    Args:
        main_object_name: 主体文件的object_name
        tender_object_name: 招标文件的object_name
        threshold: 相似度阈值，默认0.5

    Returns:
        tuple: (是否匹配, 相似度分数, 匹配原因)
    """
    # 情况1：主体文件无解析结果
    if not is_valid_field_value(main_object_name):
        return True, 1.0, "主体文件object_name为空，直接使用招标文件数据"

    # 情况2：招标文件无解析结果
    if not is_valid_field_value(tender_object_name):
        return False, 0.0, "招标文件object_name为空，无法进行匹配验证"

    # 情况3：计算文本相似度
    similarity = calculate_text_similarity(main_object_name, tender_object_name)

    if similarity >= threshold:
        if similarity == 1.0:
            return True, similarity, "object_name完全匹配"
        else:
            return (
                True,
                similarity,
                f"object_name相似度{similarity}≥{threshold}，匹配成功",
            )
    else:
        return False, similarity, f"object_name相似度{similarity}<{threshold}，匹配失败"


def is_valid_field_value(value) -> bool:
    """
    检查字段值是否有效（非空、非null）

    Args:
        value: 要检查的字段值

    Returns:
        bool: 如果值有效返回True，否则返回False
    """
    if value is None:
        return False
    if isinstance(value, str) and (value == "" or value.lower() == "null"):
        return False
    if isinstance(value, bool):
        # 布尔值False也是有效值
        return True
    if isinstance(value, (int, float)):
        # 对于数值类型，0也是有效值
        return True
    if isinstance(value, (list, dict)) and len(value) == 0:
        # 空列表和空字典视为无效
        return False
    return bool(value)


def validate_and_normalize_fields(
    document: dict, announcement_type: str = None
) -> dict:
    """
    校验和标准化文档字段，根据公告类型应用不同的字段策略

    Args:
        document: 待插入ES的文档字典
        announcement_type: 公告类型，用于确定字段策略

    Returns:
        dict: 经过校验和标准化的文档字典
    """
    if not isinstance(document, dict):
        log.warning(f"文档不是字典类型: {type(document)}")
        return {}

    # 根据公告类型确定字段策略
    if announcement_type == "001":
        log.info(f"检测到招标公告(001)，将使用限定字段策略")
    else:
        if announcement_type == "999":
            log.info(f"检测到合同公告({announcement_type})，将使用完整字段策略")
        else:
            log.info(f"公告类型: {announcement_type}，将使用完整字段策略")

    # 获取当前文档的字段
    current_fields = set(document.keys())
    standard_fields_set = set(STANDARD_FIELDS)

    # 检查多余字段（超出标准字段的字段）
    extra_fields = current_fields - standard_fields_set
    if extra_fields:
        log.info(f"发现超出标准字段的字段，将被忽略: {sorted(extra_fields)}")

    # 检查缺失字段
    missing_fields = standard_fields_set - current_fields
    if missing_fields:
        log.info(f"发现缺失字段，将补全为None: {sorted(missing_fields)}")

    # 创建标准化的文档 - 始终包含所有标准字段
    normalized_document = {}

    # 按照标准字段顺序重新组织
    for field in STANDARD_FIELDS:
        if field in document:
            # 字段存在于输入文档中
            if announcement_type == "001":
                # 招标公告：只有允许的字段保留值，其他设为None
                if field in ANNOUNCEMENT_001_FIELDS:
                    normalized_document[field] = document[field]
                else:
                    normalized_document[field] = None
            else:
                # 其他公告类型：保留所有字段的值
                normalized_document[field] = document[field]
        else:
            # 字段不存在于输入文档中，设为None
            normalized_document[field] = None

    # 统计信息
    total_fields = len(STANDARD_FIELDS)
    valid_fields = len([v for v in normalized_document.values() if v is not None])

    if announcement_type == "001":
        allowed_fields_count = len(ANNOUNCEMENT_001_FIELDS)
        log.info(
            f"字段校验完成: 标准字段{total_fields}个, 有效字段{valid_fields}个, 001类型允许字段{allowed_fields_count}个"
        )
    else:
        log.info(f"字段校验完成: 标准字段{total_fields}个, 有效字段{valid_fields}个")

    return normalized_document


def clean_json_data(json_str: str) -> str:
    """清理JSON字符串中的重复字段

    Args:
        json_str: 原始JSON字符串

    Returns:
        清理后的JSON字符串
    """
    try:
        # 先尝试解析JSON
        data = json.loads(json_str)

        # 如果是字典，确保没有重复键
        if isinstance(data, dict):
            return json.dumps(data)

        # 如果是列表，处理每个字典
        elif isinstance(data, list):
            cleaned_data = []
            for item in data:
                if isinstance(item, dict):
                    # 使用dict.fromkeys去重
                    cleaned_item = dict.fromkeys(item.keys())
                    # 保留最后一个值
                    for key in item:
                        cleaned_item[key] = item[key]
                    cleaned_data.append(cleaned_item)
            return json.dumps(cleaned_data)

        return json_str
    except json.JSONDecodeError as e:
        # 如果JSON解析失败，尝试修复
        log.warning(f"JSON解析失败，开始清理: {e}")

        # 1. 替换中文标点符号和特殊字符
        cleaned = json_str.replace("，", ",")  # 中文逗号
        cleaned = cleaned.replace("：", ":")  # 中文冒号
        cleaned = cleaned.replace('"', '"')  # 中文左引号
        cleaned = cleaned.replace('"', '"')  # 中文右引号
        cleaned = cleaned.replace("'", "'")  # 中文左单引号
        cleaned = cleaned.replace("'", "'")  # 中文右单引号

        # 替换全角数字为半角数字
        fullwidth_digits = "０１２３４５６７８９"
        halfwidth_digits = "0123456789"
        for fw, hw in zip(fullwidth_digits, halfwidth_digits):
            cleaned = cleaned.replace(fw, hw)

        # 2. 移除特殊空白字符
        import re

        # 移除全角空格和其他特殊空白字符
        cleaned = re.sub(r"[　\u3000\ufeff]+", " ", cleaned)
        # 移除行末多余的空白和特殊字符
        cleaned = re.sub(r"\s*[　\s]*$", "", cleaned, flags=re.MULTILINE)

        # 3. 智能处理重复字段（只在同一对象内去重）
        lines = cleaned.split("\n")
        cleaned_lines = []
        current_object_fields = set()
        brace_level = 0

        for line in lines:
            # 跳过空行
            if not line.strip():
                continue

            stripped_line = line.strip()

            # 跟踪大括号层级
            brace_level += stripped_line.count("{") - stripped_line.count("}")

            # 如果遇到新对象的开始，重置字段集合
            if "{" in stripped_line and brace_level <= 1:
                current_object_fields = set()

            if ":" in line:
                field = line.split(":")[0].strip().strip('"')
                if field:
                    # 只在当前对象内检查重复
                    if field not in current_object_fields:
                        current_object_fields.add(field)
                        cleaned_lines.append(line)
                    # 如果是重复字段，跳过
                else:
                    # 空字段名，保留
                    cleaned_lines.append(line)
            else:
                # 非字段行（如括号、逗号等），直接保留
                cleaned_lines.append(line)

        result = "\n".join(cleaned_lines)

        # 4. 修复常见的JSON格式问题
        # 移除对象结束前的多余逗号
        result = re.sub(r",(\s*})", r"\1", result)
        # 移除数组结束前的多余逗号
        result = re.sub(r",(\s*])", r"\1", result)
        # 修复字段名后多余的空格
        result = re.sub(r'"\s*:', r'":', result)
        # 修复缺少逗号的问题（在字符串后面跟着新行和引号的情况）
        result = re.sub(r'"\s*\n\s*"', r'",\n"', result)
        # 修复冒号前后的空格问题
        result = re.sub(r'"\s*：\s*', r'": ', result)
        # 修复冒号后缺少空格的问题
        result = re.sub(r'":([^"\s])', r'": \1', result)
        # 修复不完整的数值（如 13779. 变为 13779.0）
        result = re.sub(r"(\d+\.)(\s*[,}\]])", r"\g<1>0\2", result)
        # 移除单独成行的逗号
        result = re.sub(r"\n\s*,\s*\n", r"\n", result)
        # 修复行首的逗号
        result = re.sub(r'\n\s*,\s*"', r',\n"', result)
        # 修复字段名前的空格（如 " object_model" -> "object_model"）
        result = re.sub(r'"\s+([^"]*?)"\s*:', r'"\1":', result)
        # 修复字段名后的空格（如 "object_model " -> "object_model"）
        result = re.sub(r'"([^"]*?)\s+"\s*:', r'"\1":', result)

        # 5. 处理JSON截断问题
        # 清理不完整的最后部分
        lines = result.split("\n")
        cleaned_lines = []

        for i, line in enumerate(lines):
            stripped = line.strip()
            # 跳过只包含引号和空白的不完整行
            if stripped and not stripped in ['"', '""', '",', '":', '" ']:
                # 检查是否是不完整的字段定义
                if (
                    stripped.startswith('"')
                    and ":" not in stripped
                    and i == len(lines) - 1
                ):
                    # 最后一行是不完整的字段名，跳过
                    continue
                cleaned_lines.append(line)

        result = "\n".join(cleaned_lines)

        # 检查是否有不完整的对象或数组
        if result.strip() and not result.strip().endswith(("]", "}")):
            # 尝试修复截断的JSON
            result = result.rstrip()
            # 如果最后一行是不完整的，尝试补全
            if result.endswith(","):
                result = result.rstrip(",")

            # 计算未闭合的括号
            open_braces = result.count("{") - result.count("}")
            open_brackets = result.count("[") - result.count("]")

            # 补全缺失的闭合括号
            for _ in range(open_braces):
                result += "\n}"
            for _ in range(open_brackets):
                result += "\n]"

        # 6. 尝试再次解析
        try:
            data = json.loads(result)
            return json.dumps(data, ensure_ascii=False, indent=2)
        except json.JSONDecodeError as e2:
            log.error(f"清理后仍无法解析JSON: {e2}")
            # 尝试最后的修复：移除问题行和修复格式
            lines = result.split("\n")
            final_lines = []
            for line in lines:
                stripped = line.strip()
                # 跳过只有逗号或空白的行
                if stripped and stripped not in [",", "，"]:
                    # 修复行中的问题
                    if '":' in line and not '": ' in line:
                        line = line.replace('":', '": ')
                    final_lines.append(line)

            final_result = "\n".join(final_lines)

            # 最后一次尝试修复截断
            if final_result.strip() and not final_result.strip().endswith(("]", "}")):
                open_braces = final_result.count("{") - final_result.count("}")
                open_brackets = final_result.count("[") - final_result.count("]")
                for _ in range(open_braces):
                    final_result += "\n}"
                for _ in range(open_brackets):
                    final_result += "\n]"

            try:
                data = json.loads(final_result)
                return json.dumps(data, ensure_ascii=False, indent=2)
            except json.JSONDecodeError:
                return final_result


def process_json_data(json_str: str) -> dict:
    """处理JSON数据

    Args:
        json_str: 原始JSON字符串

    Returns:
        解析后的JSON数据
    """
    try:
        # 清理JSON数据
        cleaned_json = clean_json_data(json_str)
        # 解析JSON
        return json.loads(cleaned_json)
    except json.JSONDecodeError as e:
        log.error(f"JSON解析失败: {e}")
        raise


def clean_json_markdown(result: str) -> List[str]:
    """
    去除 markdown 代码块标记，返回多个json字符串列表

    Args:
        result: 包含多个json的markdown字符串

    Returns:
        List[str]: 清理后的json字符串列表
    """
    # 按```json分割
    parts = result.split("```json")
    json_strings = []

    for part in parts:
        if not part.strip():
            continue

        # 去除可能的```结尾
        if "```" in part:
            part = part.split("```")[0]

        # 清理并添加到列表
        cleaned = part.strip()
        if cleaned:
            json_strings.append(cleaned)

    return json_strings


def llm(
    messages: List[Dict[str, str]],
    model_name: str = "Qwen/Qwen2.5-32B-Instruct",
    model_apikey: Optional[str] = None,
    model_url: str = "https://api-inference.modelscope.cn/v1",
    temperature: float = 0,  # 新增温度参数，默认为0
    max_output_tokens: int = 8192,  # 新增最大输出token数，默认为8192
    timeout: int = 300,  # 超时时间，默认5分钟
    max_retries: int = 2,  # 最大重试次数
) -> str:
    """
    用 OpenAI 兼容API进行多轮对话，一次性返回全部内容。

    Args:
        messages (List[Dict[str, str]]): context7格式的历史消息，形如
            [
                {"role": "system", "content": "你是助手"},
                {"role": "user", "content": "问题1"},
                {"role": "assistant", "content": "回答1"},
                {"role": "user", "content": "问题2"}
            ]
        model_name (str): 模型ID，默认Qwen2.5-32B-Instruct。
        model_apikey (Optional[str]): Token。
        model_url (str): API地址，默认OpenAI兼容地址。

    Returns:
        str: LLM生成的完整回复内容。

    Raises:
        Exception: API调用失败时抛出异常。

    Example:
        >>> messages = [
        ...     {"role": "system", "content": "你是助手"},
        ...     {"role": "user", "content": "写个快排"}
        ... ]
        >>> result = llm(messages, model_apikey="xxxxxx")
        >>> print(result)
    """
    if model_apikey is None:
        raise ValueError("model_apikey不能为空，请传入Token。")

    import time

    for attempt in range(max_retries):
        try:
            # 创建客户端时设置超时
            client = OpenAI(api_key=model_apikey, base_url=model_url, timeout=timeout)

            log.info(f"正在调用LLM API (尝试 {attempt + 1}/{max_retries})...")
            # 构建请求参数
            request_params = {
                "model": model_name,
                "messages": messages,
                "stream": False,
                "temperature": temperature,
                "max_tokens": max_output_tokens,
                "response_format": {"type": "json_object"},
                "seed": 42,
            }

            # 尝试添加enable_thinking参数
            try:
                request_params["enable_thinking"] = False
                log.info("enable_thinking=False")
                response = client.chat.completions.create(**request_params)
            except TypeError:
                # 如果enable_thinking参数不被支持，移除它并重试
                request_params.pop("enable_thinking", None)
                request_params["extra_body"] = {"enable_thinking": False}
                log.info("extra_body={'enable_thinking': False}")
                response = client.chat.completions.create(**request_params)

            # 只取第一个choice的message内容
            log.info("LLM API调用成功")
            return response.choices[0].message.content.strip()

        except Exception as e:
            error_msg = str(e)
            log.error(
                f"LLM API调用失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}"
            )

            # 如果是最后一次尝试，抛出异常
            if attempt == max_retries - 1:
                raise Exception(f"LLM API调用失败 (已重试{max_retries}次): {error_msg}")

            # 检查是否是超时或网关错误，如果是则等待后重试
            if any(
                keyword in error_msg.lower()
                for keyword in ["timeout", "504", "502", "503", "gateway"]
            ):
                # wait_time = (attempt + 1) * 10  # 递增等待时间：10s, 20s, 30s
                wait_time = 1  # 等待时间：1s
                log.info(f"检测到网络错误，等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                # 其他错误直接抛出
                raise Exception(f"LLM API调用失败: {error_msg}")


def download_file(url: str, max_retries: int = 5) -> Optional[bytes]:
    """Downloads a file from a URL with enhanced retry mechanism and multiple strategies.

    Args:
        url: The URL of the file to download.
        max_retries: Maximum number of retry attempts.

    Returns:
        The file content as bytes, or None if download fails.
    """
    import time
    import random
    from urllib.parse import urlparse

    # 解析URL获取域名信息
    parsed_url = urlparse(url)
    domain = parsed_url.netloc

    # 根据域名设置特定的Referer
    if "ccgp" in domain:
        base_referer = "http://www.ccgp.gov.cn/"
        alt_referer = "https://www.ccgp.gov.cn/"
    elif "sichuan" in domain:
        base_referer = "http://www.ccgp-sichuan.gov.cn/"
        alt_referer = "https://www.ccgp-sichuan.gov.cn/"
    else:
        base_referer = f"http://{domain}/"
        alt_referer = f"https://{domain}/"

    for attempt in range(max_retries):
        try:
            log.info(f"正在下载文件 (尝试 {attempt + 1}/{max_retries}): {url}")

            # 根据尝试次数使用不同的请求策略
            if attempt == 0:
                # 第一次尝试：标准Chrome浏览器请求头
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "none",
                    "Sec-Fetch-User": "?1",
                    "Cache-Control": "max-age=0",
                    "Referer": base_referer,
                }
                timeout = 60
                session_config = {"verify": True}
            elif attempt == 1:
                # 第二次尝试：Firefox浏览器请求头
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
                    "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
                    "Accept-Encoding": "gzip, deflate",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1",
                    "Referer": alt_referer,
                }
                timeout = 90
                session_config = {"verify": True}
            elif attempt == 2:
                # 第三次尝试：Edge浏览器请求头，增加超时时间
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
                    "Accept": "*/*",
                    "Accept-Language": "zh-CN,zh;q=0.9",
                    "Accept-Encoding": "gzip, deflate",
                    "Connection": "close",  # 使用close连接
                    "Referer": base_referer,
                }
                timeout = 120
                session_config = {"verify": False}  # 跳过SSL验证
            elif attempt == 3:
                # 第四次尝试：移动端浏览器请求头
                headers = {
                    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
                    "Accept": "*/*",
                    "Accept-Language": "zh-CN,zh;q=0.9",
                    "Connection": "close",
                    "Referer": alt_referer,
                }
                timeout = 150
                session_config = {"verify": False}
            else:
                # 第五次尝试：最简请求头，最长超时时间
                headers = {
                    "User-Agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)",
                    "Accept": "*/*",
                    "Connection": "close",
                }
                timeout = 180
                session_config = {"verify": False}

            # 创建健壮的session以复用连接
            session = create_robust_session()
            session.headers.update(headers)

            # 配置session
            if not session_config.get("verify", True):
                import urllib3

                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            response = session.get(
                url,
                timeout=timeout,
                allow_redirects=True,
                verify=session_config.get("verify", True),
                stream=True,  # 使用流式下载
            )
            response.raise_for_status()

            # 流式读取内容，避免大文件内存问题
            content = b""
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    content += chunk

            session.close()
            log.info(f"文件下载成功: {url} (大小: {len(content)} 字节)")
            return content
        except requests.exceptions.RequestException as e:
            error_msg = str(e)
            log.error(f"文件下载失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")

            # 确保session被关闭
            try:
                session.close()
            except:
                pass

            # 如果是最后一次尝试，返回None
            if attempt == max_retries - 1:
                log.error(f"文件下载最终失败: {url}")
                return None

            # 根据错误类型决定等待时间和重试策略
            wait_time = 1  # 默认等待时间
            should_retry = True

            if "403" in error_msg or "forbidden" in error_msg.lower():
                # 403错误，可能是反爬虫机制
                wait_time = random.randint(3, 8)  # 随机等待3-8秒
                log.info(
                    f"检测到403错误，可能触发反爬虫机制，等待 {wait_time} 秒后重试..."
                )
            elif "404" in error_msg or "not found" in error_msg.lower():
                # 404错误，文件不存在，不需要重试
                log.error("文件不存在(404)，停止重试")
                return None
            elif any(
                keyword in error_msg.lower() for keyword in ["timeout", "timed out"]
            ):
                # 超时错误，增加等待时间
                wait_time = min(
                    10 + attempt * 5, 30
                )  # 递增等待：10s, 15s, 20s, 25s, 30s
                log.info(f"检测到超时错误，等待 {wait_time} 秒后重试...")
            elif any(
                keyword in error_msg.lower()
                for keyword in ["connection", "remote end closed"]
            ):
                # 连接错误，使用随机等待避免同时重试
                wait_time = random.randint(2, 6) + attempt * 2  # 随机等待2-6秒 + 递增
                log.info(f"检测到连接错误，等待 {wait_time} 秒后重试...")
            elif any(
                keyword in error_msg.lower()
                for keyword in ["502", "503", "504", "gateway", "service unavailable"]
            ):
                # 服务器错误，使用较长等待时间
                wait_time = random.randint(5, 15) + attempt * 3  # 随机等待5-15秒 + 递增
                log.info(f"检测到服务器错误，等待 {wait_time} 秒后重试...")
            elif "ssl" in error_msg.lower() or "certificate" in error_msg.lower():
                # SSL证书错误，后续尝试会跳过SSL验证
                wait_time = 2
                log.info(
                    f"检测到SSL错误，等待 {wait_time} 秒后重试（后续将跳过SSL验证）..."
                )
            elif "too many requests" in error_msg.lower() or "429" in error_msg:
                # 请求过于频繁
                wait_time = random.randint(10, 20) + attempt * 5  # 较长等待时间
                log.info(f"检测到请求频率限制，等待 {wait_time} 秒后重试...")
            else:
                # 其他未知错误，使用默认策略
                wait_time = random.randint(1, 3) + attempt
                log.warning(f"遇到未知错误，等待 {wait_time} 秒后重试: {error_msg}")

            # 执行等待
            if should_retry:
                time.sleep(wait_time)
        except Exception as e:
            # 处理其他异常
            log.error(
                f"下载过程中发生意外错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}"
            )
            try:
                session.close()
            except:
                pass

            if attempt == max_retries - 1:
                log.error(f"文件下载最终失败: {url}")
                return None

            # 对于意外错误，使用较短的等待时间
            wait_time = random.randint(1, 3)
            log.info(f"等待 {wait_time} 秒后重试...")
            time.sleep(wait_time)

    return None


def download_file_with_fallback(url: str) -> Optional[bytes]:
    """
    使用多种策略下载文件的备用方法

    Args:
        url: 文件URL

    Returns:
        文件内容或None
    """
    # 首先尝试标准下载方法
    content = download_file(url, max_retries=5)
    if content:
        return content

    log.info(f"标准下载失败，尝试备用下载策略: {url}")

    # 备用策略1：使用urllib
    try:
        import urllib.request
        import urllib.error

        log.info("尝试使用urllib下载...")

        # 创建请求对象
        req = urllib.request.Request(
            url,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            },
        )

        with urllib.request.urlopen(req, timeout=120) as response:
            content = response.read()
            log.info(f"urllib下载成功: {url} (大小: {len(content)} 字节)")
            return content

    except Exception as e:
        log.warning(f"urllib下载失败: {str(e)}")

    # 备用策略2：使用requests但禁用所有验证和压缩
    try:
        log.info("尝试使用简化requests下载...")

        import urllib3

        urllib3.disable_warnings()

        response = requests.get(
            url,
            headers={"User-Agent": "curl/7.68.0"},
            timeout=180,
            verify=False,
            allow_redirects=True,
            stream=False,
        )

        if response.status_code == 200:
            content = response.content
            log.info(f"简化requests下载成功: {url} (大小: {len(content)} 字节)")
            return content
        else:
            log.warning(f"简化requests下载失败，状态码: {response.status_code}")

    except Exception as e:
        log.warning(f"简化requests下载失败: {str(e)}")

    # 备用策略3：尝试分段下载
    try:
        log.info("尝试分段下载...")

        # 先获取文件大小
        head_response = requests.head(
            url,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            timeout=60,
            verify=False,
            allow_redirects=True,
        )

        if head_response.status_code == 200:
            content_length = head_response.headers.get("Content-Length")
            if content_length:
                file_size = int(content_length)
                log.info(f"文件大小: {file_size} 字节，开始分段下载...")

                # 分段下载（每段1MB）
                chunk_size = 1024 * 1024  # 1MB
                content = b""

                for start in range(0, file_size, chunk_size):
                    end = min(start + chunk_size - 1, file_size - 1)

                    headers = {
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                        "Range": f"bytes={start}-{end}",
                    }

                    chunk_response = requests.get(
                        url, headers=headers, timeout=60, verify=False
                    )

                    if chunk_response.status_code in [200, 206]:
                        content += chunk_response.content
                        log.debug(f"下载进度: {len(content)}/{file_size} 字节")
                    else:
                        raise Exception(
                            f"分段下载失败，状态码: {chunk_response.status_code}"
                        )

                log.info(f"分段下载成功: {url} (大小: {len(content)} 字节)")
                return content

    except Exception as e:
        log.warning(f"分段下载失败: {str(e)}")

    log.error(f"所有下载策略均失败: {url}")
    return None


def parse_pdf(file_content: bytes) -> str:
    """Parses text from a PDF file using available PDF libraries.

    Args:
        file_content: The content of the PDF file in bytes.

    Returns:
        The extracted text.
    """
    text = ""

    # 尝试使用pdfplumber（推荐）
    if PDF_PARSER == "pdfplumber":
        try:
            import io

            with pdfplumber.open(io.BytesIO(file_content)) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            log.debug(
                f"Successfully parsed PDF with pdfplumber, extracted {len(text)} characters"
            )
            return text
        except Exception as e:
            log.error(f"pdfplumber failed to parse PDF: {e}")

    # 如果没有可用的PDF解析库
    else:
        log.warning(
            "No PDF parsing library available. Please install pdfplumber: pip install pdfplumber"
        )

    return text


def convert_to_markdown_with_markitdown(file_content: bytes, file_ext: str) -> str:
    """
    使用markitdown将文档内容转换为Markdown格式

    Args:
        file_content: 文件内容字节
        file_ext: 文件扩展名 (如 '.pdf', '.docx')

    Returns:
        转换后的Markdown文本
    """
    if not MARKITDOWN_AVAILABLE:
        log.warning("markitdown不可用，回退到普通文本解析")
        if file_ext.lower() == ".pdf":
            return parse_pdf(file_content)
        elif file_ext.lower() == ".docx":
            return parse_docx(file_content)
        elif file_ext.lower() == ".doc":
            return parse_doc(file_content)
        else:
            return ""

    try:
        import tempfile
        import os

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=file_ext, delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # 使用markitdown转换
            md = MarkItDown()
            result = md.convert(temp_file_path)

            if result and hasattr(result, "text_content"):
                markdown_content = result.text_content
                log.debug(
                    f"markitdown成功转换{file_ext}文件，生成{len(markdown_content)}字符的Markdown"
                )
                return markdown_content
            else:
                log.warning(f"markitdown转换{file_ext}文件失败，结果为空")
                return ""

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        log.error(f"markitdown转换{file_ext}文件失败: {e}")
        # 回退到原有的解析方法
        log.info(f"回退到传统方法解析{file_ext}文件")
        if file_ext.lower() == ".pdf":
            return parse_pdf(file_content)
        elif file_ext.lower() == ".docx":
            return parse_docx(file_content)
        elif file_ext.lower() == ".doc":
            return parse_doc(file_content)
        else:
            log.warning(f"不支持的文件类型: {file_ext}")
            return ""


def parse_docx(file_content: bytes) -> str:
    """Parses text from a DOCX file.

    Args:
        file_content: The content of the DOCX file in bytes.

    Returns:
        The extracted text.
    """
    text = ""
    try:
        document = docx.Document(BytesIO(file_content))
        for para in document.paragraphs:
            text += para.text + "\n"
    except Exception as e:
        log.error(f"Failed to parse DOCX: {e}")
    return text


def parse_doc(file_content: bytes) -> str:
    """
    Parses text from a .doc file using multiple methods with fallback.

    Methods tried in order:
    1. python-docx2txt (if available)
    2. antiword command-line utility (if available)
    3. Basic text extraction attempt

    Args:
        file_content: The content of the .doc file in bytes.

    Returns:
        The extracted text.
    """
    text = ""
    temp_file_path = None

    try:
        # Method 1: Try python-docx2txt
        try:
            import docx2txt

            with tempfile.NamedTemporaryFile(delete=False, suffix=".doc") as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            text = docx2txt.process(temp_file_path)
            if text and text.strip():
                log.info("成功使用docx2txt解析.doc文件")
                return text
            else:
                log.warning("docx2txt解析.doc文件结果为空，尝试其他方法")
        except ImportError:
            log.debug("docx2txt不可用，尝试其他方法")
        except Exception as e:
            log.warning(f"docx2txt解析.doc文件失败: {e}，尝试其他方法")

        # Method 2: Try antiword command-line utility
        try:
            if temp_file_path is None:
                with tempfile.NamedTemporaryFile(
                    delete=False, suffix=".doc"
                ) as temp_file:
                    temp_file.write(file_content)
                    temp_file_path = temp_file.name

            # Use antiword to extract text
            # 尝试不同的antiword参数格式
            antiword_commands = [
                ["antiword", temp_file_path],  # 基本命令
                ["antiword", "-t", temp_file_path],  # 纯文本输出
                ["antiword", "-w", "0", temp_file_path],  # 无行宽限制
            ]

            result = None
            for cmd in antiword_commands:
                try:
                    result = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        encoding="utf-8",
                        errors="ignore",
                        timeout=30,
                    )
                    if result.returncode == 0:
                        break
                except Exception as e:
                    log.debug(f"antiword命令 {cmd} 失败: {e}")
                    continue

            if result and result.returncode == 0:
                text = result.stdout
                if text and text.strip():
                    log.info("成功使用antiword解析.doc文件")
                    return text
                else:
                    log.warning("antiword解析.doc文件结果为空")
            elif result:
                log.warning(
                    f"antiword解析失败，退出码: {result.returncode}, 错误: {result.stderr}"
                )
            else:
                log.warning("所有antiword命令格式都失败了")

        except FileNotFoundError:
            log.debug("antiword命令未找到，跳过此方法")
        except subprocess.TimeoutExpired:
            log.warning("antiword解析超时，跳过此方法")
        except Exception as e:
            log.warning(f"antiword解析.doc文件失败: {e}")

        # Method 3: Basic text extraction attempt (last resort)
        try:
            # 尝试基本的文本提取（可能不完美，但总比没有好）
            text_content = file_content.decode("utf-8", errors="ignore")
            # 简单清理，移除控制字符
            import re

            text_content = re.sub(
                r"[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]", "", text_content
            )
            # 提取可能的文本内容
            lines = []
            for line in text_content.split("\n"):
                line = line.strip()
                if len(line) > 3 and any(c.isalpha() for c in line):  # 包含字母的行
                    lines.append(line)

            if lines:
                text = "\n".join(lines)
                log.info("使用基本文本提取方法解析.doc文件")
                return text
            else:
                log.warning("基本文本提取方法未找到有效内容")

        except Exception as e:
            log.warning(f"基本文本提取失败: {e}")

        # 如果所有方法都失败了
        log.error("所有.doc文件解析方法都失败了")
        return ""

    finally:
        # Clean up the temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except:
                pass


def handle_compressed_file(file_content: bytes, original_filename: str) -> List[Dict]:
    """
    Decompresses a file and parses its contents.
    Requires patool and underlying archivers (e.g., 7z, unrar) to be installed.

    Args:
        file_content: The byte content of the compressed file.
        original_filename: The original filename of the archive.

    Returns:
        A list of dicts, each containing 'filename' and 'content' of a parsed file.
    """
    extracted_files_content = []
    extract_dir = tempfile.mkdtemp()
    archive_path = os.path.join(extract_dir, original_filename)

    try:
        # Save the archive to a temporary file
        with open(archive_path, "wb") as f:
            f.write(file_content)

        # Extract the archive
        patoolib.extract_archive(archive_path, outdir=extract_dir)
        log.info(f"成功从 {original_filename} 解压文件到 {extract_dir}")

        # Walk through the extracted files
        for root, _, files in os.walk(extract_dir):
            for filename in files:
                # Don't process the original archive file itself or hidden files
                if filename == original_filename or filename.startswith("."):
                    continue

                file_path = os.path.join(root, filename)
                file_ext = os.path.splitext(filename)[1].lower()
                parsed_content = ""

                try:
                    with open(file_path, "rb") as f_inner:
                        inner_content = f_inner.read()

                    if file_ext == ".pdf":
                        parsed_content = parse_pdf(inner_content)
                    elif file_ext == ".docx":
                        parsed_content = parse_docx(inner_content)
                    elif file_ext == ".doc":
                        parsed_content = parse_doc(inner_content)
                    else:
                        log.info(f"跳过压缩包内不支持的文件类型: {filename}")
                        continue

                    if parsed_content:
                        markdown_content = md(parsed_content)
                        extracted_files_content.append(
                            {
                                "filename": filename,
                                "content": markdown_content,
                                "file_ext": file_ext,
                                "file_bytes": inner_content,
                            }
                        )
                except Exception as e:
                    log.error(f"解析压缩包内文件 '{filename}' 失败: {e}")

    except patoolib.util.PatoolError as e:
        log.error(
            f"解压失败 {original_filename}: {e}. 请确保已安装底层解压工具 (如 7z, unrar)."
        )
    except Exception as e:
        log.error(f"处理压缩文件 {original_filename} 时发生错误: {e}")
    finally:
        # Clean up the temporary directory
        shutil.rmtree(extract_dir)

    return extracted_files_content


def get_file_extension_from_url(url: str) -> str:
    """
    Extracts the file extension from a URL's path.

    Args:
        url (str): The URL to parse.

    Returns:
        str: The file extension in lowercase (e.g., '.pdf').
    """
    try:
        path = urlparse(url).path
        ext = os.path.splitext(path)[1].lower()
        return ext
    except Exception:
        return ""


def get_file_info_from_content(file_content: bytes) -> Optional[Dict[str, str]]:
    """
    Analyzes file content to determine its type and extension.

    Args:
        file_content: The byte content of the file.

    Returns:
        A dict with 'ext' and 'mime' or None if the type is not supported/found.
    """
    if not file_content:
        return None
    try:
        kind = filetype.guess(file_content)
        if kind is None:
            log.warning("Cannot guess file type from content.")
            return None
        log.info(f"Guessed file type: MIME={kind.mime}, EXT=.{kind.extension}")
        # The library gives 'docx' but we want '.docx'
        ext = kind.extension
        if not ext.startswith("."):
            ext = "." + ext
        return {"ext": ext, "mime": kind.mime}
    except Exception as e:
        log.error(f"Error guessing file type: {e}")
        return None


def extract_preview_text(file_ext: str, file_content: bytes) -> str:
    """
    提取前3页/段的文本预览
    """
    if file_ext == ".pdf":
        text = ""

        # 尝试使用pdfplumber（推荐）
        if PDF_PARSER == "pdfplumber":
            try:
                import io

                with pdfplumber.open(io.BytesIO(file_content)) as pdf:
                    for i, page in enumerate(pdf.pages):
                        if i >= 3:  # 只提取前3页
                            break
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                log.debug(
                    f"Successfully extracted PDF preview with pdfplumber, {len(text)} characters"
                )
                return text
            except Exception as e:
                log.error(f"pdfplumber failed to extract PDF preview: {e}")

        # 如果没有可用的PDF解析库
        else:
            log.warning("No PDF parsing library available for preview extraction.")

        return text
    elif file_ext == ".docx":
        text = ""
        try:
            document = docx.Document(BytesIO(file_content))
            for i, para in enumerate(document.paragraphs):
                if i >= 12:  # 近似3页
                    break
                text += para.text + "\n"
        except Exception as e:
            log.error(f"DOCX预览提取失败: {e}")
        return text
    elif file_ext == ".doc":
        try:
            # antiword不支持分页，截取前1200字
            return parse_doc(file_content)[:1200]
        except Exception as e:
            log.error(f"DOC预览提取失败: {e}")
            return ""
    else:
        return ""


def detect_file_type(appendix_text: str, preview_text: str) -> str:
    """
    根据text和内容预览判断文件类型
    """
    # 合并文件名和内容进行检测
    text = (appendix_text or "") + "\n" + (preview_text or "")

    # 检查招标文件关键词
    matched_tender_keywords = [kw for kw in TENDER_KEYWORDS if kw in text]
    if matched_tender_keywords:
        log.info(
            f"此文件是招标文件: {appendix_text} (匹配关键词: {matched_tender_keywords})"
        )
        return "招标文件"

    # 检查合同文件关键词
    matched_contract_keywords = [kw for kw in CONTRACT_KEYWORDS if kw in text]
    if matched_contract_keywords:
        log.info(
            f"此文件是合同文件: {appendix_text} (匹配关键词: {matched_contract_keywords})"
        )
        return "合同文件"

    # # 如果内容解析失败或为空，基于文件名进行更宽松的判断
    # if not preview_text or len(preview_text.strip()) < 10:
    #     log.warning(f"文件内容解析失败或内容过少，基于文件名进行判断: {appendix_text}")

    #     # 基于文件名的宽松判断
    #     filename_lower = (appendix_text or "").lower()

    #     # 招标相关的文件名模式
    #     tender_patterns = [
    #         "招标",
    #         "采购",
    #         "谈判",
    #         "磋商",
    #         "竞争性磋商邀请",
    #         "询价",
    #         "比选",
    #         "询比",
    #         "竞价",
    #         "单一来源",
    #         "竞争性谈判",
    #     ]
    #     # 合同相关的文件名模式
    #     contract_patterns = ["合同", "服务协议"]

    #     tender_matches = [p for p in tender_patterns if p in filename_lower]
    #     contract_matches = [p for p in contract_patterns if p in filename_lower]

    #     if tender_matches:
    #         log.info(
    #             f"基于文件名判断为招标文件: {appendix_text} (匹配模式: {tender_matches})"
    #         )
    #         return "招标文件"
    #     elif contract_matches:
    #         log.info(
    #             f"基于文件名判断为合同文件: {appendix_text} (匹配模式: {contract_matches})"
    #         )
    #         return "合同文件"

    log.info(f"此文件是其他文件: {appendix_text}")
    return "其他"


def find_matching_by_object_name(
    target_object_name: str, source_list: List[dict]
) -> dict:
    """
    根据object_name在源列表中查找匹配的记录

    Args:
        target_object_name: 目标标的物名称
        source_list: 源记录列表

    Returns:
        dict: 匹配的记录，如果没找到则返回空字典
    """
    # 确保target_object_name不是None或空字符串，source_list不为空
    if not target_object_name or target_object_name is None or not source_list:
        return {}

    # 精确匹配
    for item in source_list:
        if item.get("object_name") == target_object_name:
            return item

    # 如果精确匹配失败，尝试模糊匹配（包含关系和关键词匹配）
    target_name_lower = target_object_name.lower().strip()

    # 提取关键词进行匹配
    def extract_keywords(name):
        """提取标的物名称中的关键词"""
        # 移除常见的修饰词
        name = (
            name.replace("设备", "")
            .replace("仪器", "")
            .replace("机器", "")
            .replace("系统", "")
        )
        name = (
            name.replace("扫描", "")
            .replace("检测", "")
            .replace("监护", "")
            .replace("治疗", "")
        )
        # 保留核心关键词
        keywords = []
        if "CT" in name.upper():
            keywords.append("CT")
        if "MRI" in name.upper() or "核磁" in name:
            keywords.append("MRI")
        if "超声" in name:
            keywords.append("超声")
        if "心电" in name:
            keywords.append("心电")
        if "血压" in name:
            keywords.append("血压")
        if "呼吸" in name:
            keywords.append("呼吸")
        # 如果没有特殊关键词，返回清理后的名称
        return keywords if keywords else [name.strip()]

    target_keywords = extract_keywords(target_name_lower)

    for item in source_list:
        item_name = item.get("object_name") or ""
        item_name = item_name.lower().strip()
        if item_name:
            # 先尝试包含关系匹配
            if target_name_lower in item_name or item_name in target_name_lower:
                return item

            # 再尝试关键词匹配
            item_keywords = extract_keywords(item_name)
            if any(tk in item_keywords for tk in target_keywords):
                return item

    return {}


def merge_analysis(main: dict, tender: dict, contract: dict) -> dict:
    """
    按优先级融合公告主体、招标文件、合同文件的解析结果
    main: 公告主体解析结果
    tender: 附件招标文件解析结果
    contract: 附件合同文件解析结果
    """
    result = main.copy() if main else {}

    # 先用招标文件补充（除CONTRACT_FIELDS外的字段）
    # 但需要先检查object_name匹配条件
    tender_can_supplement = False
    if tender:
        # 检查是否可以使用招标文件进行补充
        main_object_name = result.get("object_name")
        tender_object_name = tender.get("object_name")

        is_match, similarity, match_reason = check_object_name_match(
            main_object_name, tender_object_name
        )

        if is_match:
            tender_can_supplement = True
            log.debug(f"招标文件可用于基础补充: {match_reason}")
        else:
            log.debug(f"招标文件不可用于基础补充: {match_reason}")

        # 只有在匹配的情况下才进行基础补充
        if tender_can_supplement:
            for k, v in tender.items():
                if k not in CONTRACT_FIELDS:
                    # 对于招标文件相关的附件字段，只有当主结果为空且附件结果有效时才使用
                    if k in [
                        "bid_doc_name",
                        "bid_doc_ext",
                        "bid_doc_link_out",
                        "bid_doc_link_key",
                    ]:
                        # 只有当主结果中该字段为空且招标文件中有有效值时才覆盖
                        if (
                            (k not in result) or not is_valid_field_value(result[k])
                        ) and is_valid_field_value(v):
                            result[k] = v
                            log.debug(f"从招标文件补充附件字段 {k}: {v}")
                        elif is_valid_field_value(result[k]):
                            log.debug(
                                f"保留主结果中的附件字段 {k}: {result[k]} (招标文件值: {v})"
                            )
                    # 对于其他字段，只有当result[k]为None或不存在时才补充，且值必须有效
                    elif (
                        (k not in result) or (result[k] is None)
                    ) and is_valid_field_value(v):
                        result[k] = v

    # 再用合同文件补充（只补CONTRACT_FIELDS和合同相关的附件字段）
    if contract:
        for k, v in contract.items():
            if k in CONTRACT_FIELDS:
                if ((k not in result) or (result[k] is None)) and is_valid_field_value(
                    v
                ):
                    result[k] = v
            # 对于合同相关的附件字段，只有当主结果为空且合同结果有效时才使用
            elif k in [
                "contract_name",
                "contract_ext",
                "contract_link_out",
                "contract_link_key",
            ]:
                # 只有当主结果中该字段为空且合同文件中有有效值时才覆盖
                if (
                    (k not in result) or not is_valid_field_value(result[k])
                ) and is_valid_field_value(v):
                    result[k] = v
                    log.debug(f"从合同文件补充附件字段 {k}: {v}")
                elif is_valid_field_value(result[k]):
                    log.debug(
                        f"保留主结果中的附件字段 {k}: {result[k]} (合同文件值: {v})"
                    )

    # === 新增：有条件的字段覆盖机制 ===

    # 招标文件字段覆盖：如果前面已经确定可以使用招标文件，则执行覆盖
    tender_overrides = []
    if tender and tender_can_supplement:
        # 已经在基础补充阶段验证过匹配条件，直接执行覆盖
        log.info("招标文件已通过匹配验证，执行字段覆盖")

        for field in TENDER_OVERRIDE_FIELDS:
            if field in tender and is_valid_field_value(tender[field]):
                old_value = result.get(field)
                result[field] = tender[field]
                tender_overrides.append(f"{field}: '{old_value}' → '{tender[field]}'")

        if tender_overrides:
            log.info(
                f"招标文件覆盖字段: {', '.join([f.split(':')[0] for f in tender_overrides])}"
            )
            for override in tender_overrides:
                log.debug(f"  {override}")
    elif tender:
        # 招标文件存在但不满足匹配条件
        main_object_name = result.get("object_name")
        tender_object_name = tender.get("object_name")
        _, similarity, _ = check_object_name_match(main_object_name, tender_object_name)

        log.warning(
            f"招标文件object_name不匹配，跳过字段覆盖: "
            f"主体='{main_object_name}' vs 招标='{tender_object_name}' "
            f"(相似度: {similarity})"
        )

    # 合同文件字段覆盖：如果合同文件中有有效值，则覆盖融合结果
    contract_overrides = []
    if contract:
        for field in CONTRACT_OVERRIDE_FIELDS:
            if field in contract and is_valid_field_value(contract[field]):
                old_value = result.get(field)
                result[field] = contract[field]
                contract_overrides.append(
                    f"{field}: '{old_value}' → '{contract[field]}'"
                )

    if contract_overrides:
        log.info(
            f"合同文件覆盖字段: {', '.join([f.split(':')[0] for f in contract_overrides])}"
        )
        for override in contract_overrides:
            log.debug(f"  {override}")

    return result


def ensure_list(obj):
    """确保返回list[dict]"""
    if isinstance(obj, list):
        return [x for x in obj if isinstance(x, dict)]
    elif isinstance(obj, dict):
        return [obj]
    else:
        return []


def contains_contract_keywords(content: str, keywords: List[str] = None) -> bool:
    """
    检查内容是否包含合同相关关键词

    Args:
        content (str): 要检查的文本内容
        keywords (List[str], optional): 关键词列表，默认使用CONTRACT_KEYWORDS

    Returns:
        bool: 如果内容包含任一关键词则返回True，否则返回False
    """
    if not content:
        return False

    if keywords is None:
        keywords = CONTRACT_KEYWORDS

    content_lower = content.lower()
    for keyword in keywords:
        if keyword.lower() in content_lower:
            log.debug(f"在内容中找到合同关键词: {keyword}")
            return True

    return False


def merge_analysis_by_object_name(
    main_list: List[dict], tender_list: List[dict], contract_list: List[dict]
) -> List[dict]:
    """
    基于object_name进行匹配融合的分析函数

    Args:
        main_list: 主体解析结果列表
        tender_list: 招标文件解析结果列表
        contract_list: 合同文件解析结果列表

    Returns:
        List[dict]: 融合后的结果列表
    """
    results = []

    # 如果主体解析结果为空，直接处理招标文件和合同文件的融合
    if not main_list:
        if tender_list and contract_list:
            # 招标文件和合同文件之间的匹配融合
            for tender_item in tender_list:
                tender_object_name = tender_item.get("object_name", "")
                matching_contract = find_matching_by_object_name(
                    tender_object_name, contract_list
                )
                merged = merge_analysis({}, tender_item, matching_contract)
                results.append(merged)
                log.info(
                    f"招标文件标的物 '{tender_object_name}' 与合同文件匹配: {'是' if matching_contract else '否'}"
                )

            # 处理没有匹配到的合同文件记录
            used_contract_names = set()
            for tender_item in tender_list:
                tender_object_name = tender_item.get("object_name", "")
                matching_contract = find_matching_by_object_name(
                    tender_object_name, contract_list
                )
                if matching_contract:
                    used_contract_names.add(matching_contract.get("object_name", ""))

            for contract_item in contract_list:
                contract_object_name = contract_item.get("object_name", "")
                if contract_object_name not in used_contract_names:
                    # 直接使用合同文件的结果，确保object_name等字段不丢失
                    results.append(contract_item.copy())
                    log.info(
                        f"合同文件标的物 '{contract_object_name}' 没有匹配的招标文件，单独处理"
                    )

        elif tender_list:
            results.extend(tender_list)
            log.info("只有招标文件解析结果，直接使用")
        elif contract_list:
            results.extend(contract_list)
            log.info("只有合同文件解析结果，直接使用")
    else:
        # 主体解析结果不为空，进行基于object_name的匹配融合
        for main_item in main_list:
            main_object_name = main_item.get("object_name", "")

            # 查找匹配的招标文件和合同文件记录
            matching_tender = find_matching_by_object_name(
                main_object_name, tender_list
            )
            matching_contract = find_matching_by_object_name(
                main_object_name, contract_list
            )

            # 进行融合
            merged = merge_analysis(main_item, matching_tender, matching_contract)
            results.append(merged)

            log.info(f"主体标的物 '{main_object_name}' 匹配结果:")
            log.info(f"  - 招标文件匹配: {'是' if matching_tender else '否'}")
            log.info(f"  - 合同文件匹配: {'是' if matching_contract else '否'}")

    return results


def intelligent_merge_analysis(
    main_list: List[dict],
    tender_content: str = None,
    contract_content: str = None,
    tender_list: List[dict] = None,
    contract_list: List[dict] = None,
    model_apikey: str = None,
    model_name: str = None,
    model_url: str = None,
    timeout: int = 300,
    max_retries: int = 2,
) -> List[dict]:
    """
    智能融合分析：对主体解析结果的空缺字段进行检索和融合

    Args:
        main_list: 主体解析结果列表
        tender_content: 招标文件内容
        contract_content: 合同文件内容
        tender_list: 招标文件解析结果列表（可选）
        contract_list: 合同文件解析结果列表（可选）
        model_apikey: API密钥
        model_name: 模型名称
        model_url: 模型URL
        timeout: 超时时间
        max_retries: 最大重试次数

    Returns:
        List[dict]: 融合后的结果列表
    """
    main_list = ensure_list(main_list)
    tender_list = ensure_list(tender_list) if tender_list else []
    contract_list = ensure_list(contract_list) if contract_list else []

    # 首先使用基于object_name的匹配融合
    log.info("开始基于object_name的匹配融合分析")
    results = merge_analysis_by_object_name(main_list, tender_list, contract_list)

    # 如果没有结果，返回空列表
    if not results:
        log.info("基于object_name的匹配融合没有产生结果")
        return []

    log.info(f"基于object_name的匹配融合完成，产生{len(results)}个结果")

    # 对融合后的结果进行智能补充（如果提供了文档内容和模型配置）
    if (
        (tender_content or contract_content)
        and model_apikey
        and model_name
        and model_url
    ):
        log.info("开始对融合结果进行智能补充")
        enhanced_results = []

        for i, merged_result in enumerate(results):
            log.info(f"处理第{i+1}个融合结果的智能补充")

            # 为当前结果找到对应的招标文件和合同文件信息
            current_object_name = merged_result.get("object_name", "")
            tender_info = find_matching_by_object_name(current_object_name, tender_list)
            contract_info = find_matching_by_object_name(
                current_object_name, contract_list
            )

            # 2. 识别仍然空缺的字段
            missing_fields = identify_missing_fields(merged_result)

            if missing_fields:
                log.info(f"发现空缺字段: {missing_fields}")

                # 3. 分别从招标文件和合同文件中检索空缺字段
                tender_extracted = {}
                contract_extracted = {}

                # 分离合同相关字段和其他字段
                contract_missing_fields = [
                    f for f in missing_fields if f in CONTRACT_FIELDS
                ]
                other_missing_fields = [
                    f for f in missing_fields if f not in CONTRACT_FIELDS
                ]

                # 优先从已有的合同文件解析结果中获取合同相关字段
                if contract_missing_fields and contract_info:
                    log.info(f"从合同文件解析结果中获取字段: {contract_missing_fields}")
                    for field in contract_missing_fields:
                        if field in contract_info and contract_info[field] is not None:
                            contract_extracted[field] = contract_info[field]
                            log.info(
                                f"从合同文件解析结果获取字段 {field}: {contract_info[field]}"
                            )

                # 如果合同文件解析结果中没有，且有文档内容，再尝试LLM提取
                remaining_contract_fields = [
                    f for f in contract_missing_fields if f not in contract_extracted
                ]
                if remaining_contract_fields and contract_content and model_apikey:
                    log.info(
                        f"从合同文件内容中检索剩余字段: {remaining_contract_fields}"
                    )
                    llm_contract_extracted = extract_fields_from_content(
                        contract_content,
                        remaining_contract_fields,
                        model_apikey,
                        model_name,
                        model_url,
                        timeout,
                        max_retries,
                    )
                    contract_extracted.update(llm_contract_extracted)

                # 优先从已有的招标文件解析结果中获取其他字段
                if other_missing_fields and tender_info:
                    log.info(f"从招标文件解析结果中获取字段: {other_missing_fields}")
                    for field in other_missing_fields:
                        if field in tender_info and tender_info[field] is not None:
                            tender_extracted[field] = tender_info[field]
                            log.info(
                                f"从招标文件解析结果获取字段 {field}: {tender_info[field]}"
                            )

                # 如果招标文件解析结果中没有，且有文档内容，再尝试LLM提取
                remaining_tender_fields = [
                    f for f in other_missing_fields if f not in tender_extracted
                ]
                if remaining_tender_fields and tender_content and model_apikey:
                    # 添加智能判断：如果招标文件已经解析过但没有这些字段，
                    # 很可能这些字段在文档中不存在，避免不必要的LLM调用
                    if tender_info:
                        log.info(
                            f"招标文件已解析但缺少字段 {remaining_tender_fields}，跳过LLM重新提取"
                        )
                        log.info("建议：检查字段定义或文档内容是否包含这些信息")
                    else:
                        log.info(
                            f"从招标文件内容中检索剩余字段: {remaining_tender_fields}"
                        )
                        llm_tender_extracted = extract_fields_from_content(
                            tender_content,
                            remaining_tender_fields,
                            model_apikey,
                            model_name,
                            model_url,
                            timeout,
                            max_retries,
                        )
                        tender_extracted.update(llm_tender_extracted)

                # 4. 将检索到的字段值融合到结果中
                for field, value in tender_extracted.items():
                    if value is not None and (
                        field not in merged_result or merged_result[field] is None
                    ):
                        merged_result[field] = value
                        log.info(f"从招标文件补充字段 {field}: {value}")

                for field, value in contract_extracted.items():
                    if value is not None and (
                        field not in merged_result or merged_result[field] is None
                    ):
                        merged_result[field] = value
                        log.info(f"从合同文件补充字段 {field}: {value}")
            else:
                log.info("未发现空缺字段，跳过智能检索")

            enhanced_results.append(merged_result)
            log.info(f"第{i+1}个融合结果智能补充完成")

        log.info(f"智能补充完成，返回{len(enhanced_results)}个增强结果")
        return enhanced_results
    else:
        log.info("未提供文档内容或模型配置，直接返回基础融合结果")
        return results


def process_all_attachments(
    appendix_list: list, source_id: str, enable_file_upload: bool = True
) -> tuple[list, dict, dict]:
    """
    下载并上传所有附件文件，返回appendix_info数组、文件内容缓存和文件类型缓存

    Args:
        appendix_list: 附件列表，来自doc["_source"]["appendix"]
        source_id: 文档ID
        enable_file_upload: 是否启用文件上传功能

    Returns:
        tuple: (appendix_info数组, 文件内容缓存字典, 文件类型缓存字典)
    """
    appendix_info = []
    file_content_cache = {}  # 缓存下载的文件内容，避免重复下载
    file_type_cache = {}  # 缓存文件类型检测结果，避免重复检测

    for appendix_item in appendix_list:
        appendix_text = appendix_item.get("text", "")
        appendix_url = appendix_item.get("url")

        if not appendix_url:
            log.warning(f"附件URL为空，跳过: {appendix_text}")
            continue

        try:
            # 下载文件并缓存，使用增强的下载策略
            log.info(f"正在下载附件: {appendix_url}")
            file_content = download_file_with_fallback(appendix_url)
            if not file_content:
                log.warning(f"所有下载策略均失败，跳过: {appendix_url}")
                continue

            # 缓存文件内容，供后续分析使用
            file_content_cache[appendix_url] = file_content

            # Phase 1: 智能识别文件类型和文件名
            # 首先尝试从URL获取扩展名
            file_ext = get_file_extension_from_url(appendix_url)
            actual_filename = appendix_text

            # 对所有文件都进行文件类型检测并缓存，确保Phase 2能100%命中缓存
            file_info = get_file_info_from_content(file_content)
            if file_info:
                detected_ext = file_info.get("ext", "")
                file_type_cache[appendix_url] = file_info  # 缓存所有文件的类型信息
                log.info(f"检测并缓存文件类型: {detected_ext} for {appendix_text}")

                # 如果URL无法确定扩展名，使用检测结果
                if not file_ext:
                    file_ext = detected_ext
                    log.info(f"从文件内容确定文件类型: {file_ext}")

                    # 更新文件名
                    if file_ext and not appendix_text.endswith(file_ext):
                        actual_filename = f"{appendix_text}{file_ext}"
                        log.info(f"更新文件名: {appendix_text} -> {actual_filename}")

            # 如果文件类型检测也失败，尝试其他方法
            if not file_ext:
                # 如果文件类型检测失败，尝试下载到本地进一步分析
                log.info(f"尝试下载到本地进行进一步分析: {appendix_url}")
                download_dir = f"./downloads/{source_id}"
                os.makedirs(download_dir, exist_ok=True)

                # 生成临时文件名
                temp_filename = f"temp_{hash(appendix_url) % 10000}"
                temp_path = os.path.join(download_dir, temp_filename)

                try:
                    # 保存到临时文件
                    with open(temp_path, "wb") as f:
                        f.write(file_content)

                    # 尝试从文件头识别类型（这是第二次尝试，用于兜底）
                    file_info_local = get_file_info_from_content(file_content)
                    if file_info_local:
                        file_ext = file_info_local.get("ext", "")
                        log.info(f"从本地文件分析得到文件类型: {file_ext}")

                        # 更新缓存（如果之前没有缓存成功）
                        if appendix_url not in file_type_cache:
                            file_type_cache[appendix_url] = file_info_local

                        # 重命名文件
                        if file_ext:
                            actual_filename = f"{appendix_text}{file_ext}"
                            final_path = os.path.join(download_dir, actual_filename)
                            os.rename(temp_path, final_path)
                            log.info(f"文件已保存到: {final_path}")
                        else:
                            # 删除临时文件
                            os.remove(temp_path)
                    else:
                        # 删除临时文件
                        os.remove(temp_path)

                except Exception as e:
                    log.error(f"本地文件分析失败: {e}")
                    if os.path.exists(temp_path):
                        os.remove(temp_path)

                # 最后的兜底方案
                if not file_ext:
                    file_ext = ".bin"
                    log.warning(f"无法确定文件类型，使用通用扩展名: {appendix_url}")

            # Phase 1不进行文件类型过滤，所有文件都上传

            # 初始化附件信息
            attachment_info = {
                "url": appendix_url,
                "text": actual_filename,  # 使用实际的文件名
                "file_ext": file_ext,
                "file_link_key": None,  # 默认为None，上传成功后更新
            }

            # 上传文件（如果启用）
            if enable_file_upload:
                try:
                    log.info(f"开始上传附件: {actual_filename}")
                    upload_success, upload_id = upload_attachment_file(
                        file_content=file_content,
                        source_id=source_id,
                        original_filename=actual_filename,
                        file_ext=file_ext,
                    )

                    if upload_success and upload_id:
                        attachment_info["file_link_key"] = upload_id
                        log.info(
                            f"附件上传成功: {actual_filename}, upload_id: {upload_id}"
                        )
                    else:
                        log.warning(f"附件上传失败: {actual_filename}")

                except Exception as e:
                    log.error(f"附件上传过程中发生异常: {e}")
                    # 上传失败不影响附件信息的记录
            else:
                log.debug(f"文件上传功能已禁用，跳过上传: {actual_filename}")

            # 添加到appendix_info数组
            appendix_info.append(attachment_info)
            log.info(f"已处理附件: {actual_filename}")

        except Exception as e:
            log.error(f"处理附件时发生异常: {appendix_text}, 错误: {e}")
            # 继续处理下一个附件
            continue

    log.info(f"完成所有附件处理，共处理 {len(appendix_info)} 个附件")
    return appendix_info, file_content_cache, file_type_cache


class DocumentAnalyzer:
    def __init__(
        self,
        es_client,
        es_index_links: str,
        es_index_analysis: str,
        model_apikey: str,
        model_name: str,
        model_url: str,
        prompt_spec: str,
        timeout: int = 300,
        max_retries: int = 2,  # 新增参数model_url和model_name，用于指定模型名称和URL。
        enable_file_upload: bool = True,  # 新增参数：是否启用文件上传功能
    ):
        """
        初始化文档分析器

        Args:
            es_client: Elasticsearch客户端实例
            es_index_links: 源数据索引名
            es_index_analysis: 分析结果索引名
            model_apikey: API Token
            model_name: 模型名称
            model_url: 模型URL
            prompt_spec: 提示规范
            timeout: 超时时间
            max_retries: 最大重试次数
            enable_file_upload: 是否启用文件上传功能
        """
        self.es = es_client
        self.es_index_links = es_index_links
        self.es_index_analysis = es_index_analysis
        self.model_apikey = model_apikey
        self.model_name = model_name
        self.model_url = model_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.enable_file_upload = enable_file_upload

        # 初始化黑名单管理器
        self.blacklist_manager = BlacklistManager()

        # 打印黑名单统计信息
        stats = self.blacklist_manager.get_blacklist_stats()
        if stats["total_count"] > 0:
            log.info(f"黑名单中有 {stats['total_count']} 个文档将被跳过")
            if stats["today_count"] > 0:
                log.info(f"今日新增黑名单文档: {stats['today_count']} 个")

        # 使用传入的prompt_spec，如果为空则使用默认值
        self.prompt_spec = (
            prompt_spec
            if prompt_spec
            else """
        你是招投标行业的专家，请根据下列要求提取招标人及相关信息：
        - 使用中文回答。
        - 以 JSON 格式返回关键信息，可直接用 Python 的 json.loads 解析。
        - 金额单位统一转换为人民币元。
        - 下面字段类型是Elasticsearch数据库的数据类型。
        - **如果公告涉及多个物品（如object_name、object_brand、object_model、object_supplier、object_price等字段），请将每个物品单独作为一个对象输出，最终返回一个对象列表（list of dict），每个对象包含该物品的所有相关字段。不要将多个物品的信息合并为一个字符串。**
        - **list of dict的最外层必须是list，内部必须是dict，内部不要是list的类型。一定不要像bad case那样输出**
        - 其他非物品相关字段（如项目名称、招标人等）需要放在每个对象中。
        - 严格根据以下字典里字段的类型和描述要求提取公告中的键值对，未提取到的值赋值null，不确定的赋值null，只输出确定的结果，不要有多余注释和重复字段：
        {
            'bid_name': '标段名称,标段有时也叫包组或者包,字段类型是text',
            'bid_number': '标段编号,标段有时也叫包组或者包,字段类型是keyword',
            'bid_budget': '标段预算金额,标段有时也叫包组或者包,字段类型是double',
            'fiscal_delegation_number': '财政委托编号,字段类型是keyword',
            'prj_addr': '招标项目地址,字段类型是text',
            'prj_name': '招标项目名称,字段类型是text',
            'prj_number': '招标项目编号,字段类型是keyword',
            'prj_type': '招标项目类型(工程,货物,服务),字段类型是keyword',
            'release_time': '发布日期,字段类型是date,字段格式是yyyy-MM-dd HH:mm:ss',
            'prj_approval_authority': '项目审批单位,字段类型是text',
            'superintendent_office': '监督部门,字段类型是text',
            'superintendent_office_code': '监督部门编号,字段类型是keyword',
            'tenderee': '招标人,字段类型是text',
            'bid_submission_deadline': '投标截止时间,字段类型是date,字段格式是yyyy-MM-dd HH:mm:ss',
            'trade_platform': '交易平台,字段类型是text',
            'procurement_method': '采购方式,字段类型是text',
            'prj_sub_type': '项目细分类型(设备,维保,耗材,试剂,手术器械,其他),字段类型是keyword',
            'province': '省份,必须是XX省,字段类型是keyword',
            'city': '城市,city必须是XX市,字段类型是keyword',
            'county': '区县,必须是XX区或者XX县,但不能是市辖区,字段类型是keyword',
            'announcement_type': '公告类型(001,002,003,004,010,999),001代表招标公告,002代表候选人公示,003代表更正/澄清公告,004代表结果公告,010代表中标通知书,999代表其他,字段类型是keyword',
            'object_name': '标的物名称,字段类型是text',
            'object_brand': '标的物品牌,字段类型是keyword',
            'object_model': '标的物型号,字段类型是keyword',
            'object_supplier': '标的物供应商,字段类型是text',
            'object_produce_area': '标的物产地,字段类型是text',
            'object_conf': '标的物配置参数,它是招标需求、采购需求、项目需求里的采购需求、技术要求、技术规格、技术参数、参数要求、配置要求、规格型号、货物明细、规格参数等内容,字段类型是text',
            'object_oem': '标的物OEM厂家,字段类型是text',
            'object_amount': '标的物数量,字段类型是integer',
            'object_unit': '标的物单位,字段类型是keyword',
            'object_price': '标的物单价,只有一个单价,不存在多个单价,字段类型是double',
            'object_total_price': '标的物总价,只有一个总价,不存在多个总价,不存在多个总价,如果对象存在标的物单价(object_price)和标的物数量(object_amount)且不为""、0和null的情况下,标的物总价(object_total_price)等于标的物单价(object_price)乘以标的物数量(object_amount),字段类型是double',
            'object_maintenance_period': '标的物维保期限,字段类型是keyword',
            'object_price_source': '标的物价格来源,字段类型是keyword',
            'object_quality': '标的物质量层次(1,2),1代表国产,2代表进口,字段类型是keyword',
            'bidder_price': '中标金额,字段类型是double',
            'bidder_name': '中标单位名称,字段类型是text',
            'bidder_contact_person': '中标单位联系人,字段类型是text',
            'bidder_contact_phone_number': '中标单位联系人电话,字段类型是keyword',
            'bidder_contract_config_param': '中标合同配置参数,它是中标合同里的报价表和技术应答,字段类型是text',
            'agent': '代理机构,字段类型是text',
            'service_fee': '代理服务收费金额,字段类型是double',
            'bid_cancelled_flag': '标段是否废标标记,废标填1,否则填null,标段有时也叫包组或者包,字段类型是keyword',
            'bid_cancelled_reason': '标段废标原因,标段有时也叫包组或者包,字段类型是text',
        }
        - **输出示例：**
        [
            {
                "bid_name": null,
                "bid_number": "JXMT【2025】G123",
                "bid_budget": null,
                "fiscal_delegation_number": null,
                "prj_addr": "江西省南昌市红谷滩区碟子湖大道1399号",
                "prj_name": "南昌市洪都中医院采购骨科手术床等设备一批",
                "prj_number": "JXMT【2025】G123",
                "prj_type": "货物",
                "release_time": "2025-06-26 16:14:00",
                "prj_approval_authority": null,
                "superintendent_office": null,
                "superintendent_office_code": null,
                "tenderee": "南昌市洪都中医院",
                "bid_submission_deadline": null,
                "trade_platform": "江西省公共资源交易平台",
                "procurement_method": null,
                "prj_sub_type": "设备",
                "province": "江西省",
                "city": "南昌市",
                "county": "红谷滩区",
                "announcement_type": "004",
                "object_name": "气压止血带",
                "object_brand": "亿凡",
                "object_model": "YF-ATS-D",
                "object_supplier": "上海捷仰医疗科技发展有限公司",
                "object_produce_area": null,
                "object_conf": null,
                "object_oem": null,
                "object_amount": 5,
                "object_unit": "个",
                "object_price": 7000.0,
                "object_total_price": 35000.0,
                "object_maintenance_period": null,
                "object_price_source": null,
                "object_quality": null,
                "bidder_price": 985800.0,
                "bidder_name": "上海捷仰医疗科技发展有限公司",
                "bidder_contact_person": "胡波平",
                "bidder_contact_phone_number": "13681737817",
                "bidder_contract_config_param": null,
                "agent": "江西明台项目咨询管理有限公司",
                "service_fee": 14787.0,
                "bid_cancelled_flag": null,
                "bid_cancelled_reason": null
            },
            {
                "bid_name": "六安市裕安区独山镇中心卫生院（苏维埃红色医院）医技综合楼能力提升设备采购项目第二包",
                "bid_number": "FS34150320250102号-2",
                "bid_budget": null,
                "fiscal_delegation_number": null,
                "prj_addr": "六安市裕安区独山镇环城路43号",
                "prj_name": "六安市裕安区独山镇中心卫生院（苏维埃红色医院）医技综合楼能力提升设备采购项目",
                "prj_number": "FS34150320250102号",
                "prj_type": "货物",
                "release_time": "2025-06-26 11:05:00",
                "prj_approval_authority": null,
                "superintendent_office": null,
                "superintendent_office_code": null,
                "tenderee": "六安市裕安区独山镇中心卫生院",
                "bid_submission_deadline": null,
                "trade_platform": "中国政府采购网",
                "procurement_method": null,
                "prj_sub_type": "设备",
                "province": "安徽省",
                "city": "六安市",
                "county": "裕安区",
                "announcement_type": "004",
                "object_name": "CT",
                "object_brand": "东软",
                "object_model": "NeuViz CT",
                "object_supplier": "合肥亿行医药有限公司",
                "object_produce_area": null,
                "object_conf": null,
                "object_oem": null,
                "object_amount": 1,
                "object_unit": "台",
                "object_price": 3886000.00,
                "object_total_price": 3886000.00,
                "object_maintenance_period": null,
                "object_price_source": null,
                "object_quality": null,
                "bidder_price": 3886000.00,
                "bidder_name": "合肥亿行医药有限公司",
                "bidder_contact_person": null,
                "bidder_contact_phone_number": null,
                "bidder_contract_config_param": null,
                "agent": "安徽省六投项目管理有限公司",
                "service_fee": 46700.00,
                "bid_cancelled_flag": null,
                "bid_cancelled_reason": null
            }
        ]
        - **bad case：**
        [
            [
                2025
            ],
            [
                {
                "agent": "甘肃全标项目管理有限公司",
                "city": "酒泉市",
                "county": "瓜州县",
                "bid_submission_deadline": "2025-07-09 09:00",
                "province": "甘肃省",
                "object_name": "双能X射线骨密度仪",
                "object_quality": "2",
                "prj_name": "瓜州县人民医院双能X射线骨密度仪和肺功能仪采购项目",
                "announcement_type": "001",
                "release_time": "2025-06-18 20:40:00",
                "prj_number": "GZZFCG[2025]84号",
                "object_unit": "台",
                "bid_number": "GZZFCG[2025]84号",
                "bid_budget": 1400000.0,
                "prj_type": "货物",
                "object_conf": "需采购双能X射线骨密度仪1台（进口）",
                "trade_platform": "酒泉市公共资源交易网瓜州分中心网站",
                "tenderee": "瓜州县人民医院",
                "prj_sub_type": "设备",
                "prj_addr": "瓜州县渊泉镇文化街",
                "object_amount": 1
                },
                {
                "agent": " 甘肃全标项目管理有限公司",
                "city": "酒泉市",
                "county": "瓜州县",
                "bid_submission_deadline": "2025-07-09 09:00",
                "province": "甘肃省",
                "object_name": "肺功能仪",
                "prj_name": "瓜州县人民医院双能X射线骨密度仪和肺功能仪采购项目",
                "announcement_type": "001",
                "release_time": "2025-06-18 20:40:00",
                "prj_number": "GZZFCG[2025]84号",
                "object_unit": "台",
                "bid_number": "GZZFCG[2025]84号",
                "bid_budget": 1400000.0,
                "prj_type": "货物",
                "object_conf": "需采购肺功能仪1台",
                "trade_platform": "酒泉市公共资源交易网瓜州分中心网站",
                "tenderee": "瓜州县人民医院",
                "prj_sub_type": "设备",
                "prj_addr": "瓜州县渊泉镇文化街",
                "object_amount": 1
                }
            ]
        ]
        """
        )

    def build_query(
        self,
        categories: List[str],
        exclude_ids: List[str] = None,
    ) -> Dict:
        """
        构建ES查询

        Args:
            categories: 分类列表
            exclude_ids: 需要排除的ID列表

        Returns:
            Dict: ES查询语句
        """
        must_conditions = [
            {"terms": {"category": categories}},
            # 使用context7方式检查appendix字段
            {
                "bool": {
                    "must": [
                        {
                            "nested": {
                                "path": "appendix",
                                "query": {
                                    "bool": {
                                        "must": [
                                            {"exists": {"field": "appendix.text"}},
                                            {"exists": {"field": "appendix.url"}},
                                        ]
                                    }
                                },
                            }
                        }
                    ]
                }
            },
        ]

        if exclude_ids:
            must_conditions.append(
                {"bool": {"must_not": [{"ids": {"values": exclude_ids}}]}}
            )

        return {
            "query": {"bool": {"must": must_conditions}},
            "size": 1,
            "sort": [{"_id": {"order": "asc"}}],
        }

    def get_processed_ids(self) -> List[str]:
        """
        获取已处理的文档ID列表

        Returns:
            List[str]: 已处理的文档ID列表
        """
        processed_ids = []

        # 初始化scroll查询，指定需要返回source_id字段
        query = {
            "query": {"match_all": {}},
            "_source": ["source_id"],  # 只返回source_id字段
            "size": 10000,
        }

        # 执行初始搜索
        result = search_documents(self.es, self.es_index_analysis, query=query)
        if not result:
            return processed_ids

        # 获取第一批结果
        hits = result.get("hits", {}).get("hits", [])
        # 从source中获取source_id
        processed_ids.extend([hit["_source"]["source_id"] for hit in hits])

        # 获取scroll_id
        scroll_id = result.get("_scroll_id")
        if not scroll_id:
            return processed_ids

        # 使用scroll API获取剩余结果
        while True:
            scroll_result = self.es.scroll(
                scroll_id=scroll_id, scroll="5m"  # 保持scroll上下文5分钟
            )

            hits = scroll_result.get("hits", {}).get("hits", [])
            if not hits:
                break

            # 从source中获取source_id
            processed_ids.extend([hit["_source"]["source_id"] for hit in hits])

        # 清理scroll上下文
        self.es.clear_scroll(scroll_id=scroll_id)

        # 去重
        processed_ids = list(set(processed_ids))

        return processed_ids

    def analyze_content(self, content: str, title: str) -> List[dict]:
        """
        使用LLM分析给定的内容

        Args:
            content (str): 要分析的文本内容.
            title (str): 公告标题.

        Returns:
            List[dict]: 分析结果的列表.
        """
        # 检查内容长度，避免过长内容导致超时
        max_content_length = 131072  # 最大128K字符
        if len(content) > max_content_length:
            log.warning(
                f"文档内容过长({len(content)}字符)，截取前{max_content_length}字符"
            )
            content = content[:max_content_length] + "\n...(内容已截取)"
        prompt = f"""
        标题：{title}
        内容：
        {content}
        {self.prompt_spec}
        """

        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": "严格按照系统提示词输出结果，不要胡编乱造"},
        ]

        result = llm(
            messages,
            model_name=self.model_name,
            model_apikey=self.model_apikey,
            model_url=self.model_url,
            timeout=self.timeout,
            max_retries=self.max_retries,
        )
        log.info(result)

        json_strings = clean_json_markdown(result)
        analyzed_results = []

        for json_str in json_strings:
            try:
                llm_result = process_json_data(json_str)
                analyzed_results.append(llm_result)
            except json.JSONDecodeError as e:
                log.error(f"JSON解析失败: {e}")
                log.error(f"原始JSON字符串: {json_str}")
                continue

        return analyzed_results

    def analyze_document(self, doc: Dict) -> List[Dict]:
        """
        分析单个文档的主体内容

        Args:
            doc: ES文档

        Returns:
            List[Dict]: 分析结果列表
        """
        title = doc["_source"]["title"]
        html_content = doc["_source"]["response"]
        content = md(html_content)

        return self.analyze_content(content, title)

    def process_one_record(
        self,
        categories: List[str] = ["001", "999"],
    ):
        """
        处理一条记录

        Args:
            categories: 分类列表
        """
        try:
            # 统计变量：用于记录公告类型999的标题过滤情况
            skipped_documents_999_title = 0
            processed_documents_999_title = 0

            # 获取已处理的ID列表
            processed_ids = self.get_processed_ids()

            # 获取黑名单ID列表
            blacklisted_ids = self.blacklist_manager.get_blacklisted_ids()

            # 合并排除列表
            exclude_ids = list(set(processed_ids + blacklisted_ids))

            if blacklisted_ids:
                log.info(f"排除 {len(blacklisted_ids)} 个黑名单文档")

            # 构建查询，排除已处理的ID和黑名单ID
            query = self.build_query(categories, exclude_ids=exclude_ids)
            result = search_documents(self.es, self.es_index_links, query=query)

            if result and result.get("hits", {}).get("hits"):
                hits = result["hits"]["hits"]
                if hits:
                    doc = hits[0]  # 只处理第一条记录

                    # 针对公告类型999的标题内容过滤逻辑
                    category = doc["_source"].get("category")

                    if "999" == category:
                        source_title = doc["_source"].get("title", "")
                        if not contains_contract_keywords(source_title):
                            skipped_documents_999_title += 1
                            log.info(
                                f"公告类型999：标题内容不包含合同关键词，跳过分析: {source_title}"
                            )
                            log.debug(f"标题内容: {source_title}")

                            # 将跳过的文档添加到黑名单
                            doc_id = doc["_id"]
                            doc_url = doc["_source"].get("url", "")

                            success = self.blacklist_manager.add_to_blacklist(
                                document_id=doc_id,
                                document_title=source_title,
                                document_url=doc_url,
                                failure_reason="公告类型999：标题不包含合同关键词（合同、服务协议）",
                            )

                            if success:
                                log.info(
                                    f"✓ 文档 {doc_id} 已添加到黑名单，下次运行将跳过"
                                )
                            else:
                                log.warning(f"✗ 添加文档 {doc_id} 到黑名单失败")

                            # 输出标题过滤统计信息
                            log.info("=" * 60)
                            log.info("公告类型999标题过滤统计:")
                            log.info(f"  跳过的文档数: {skipped_documents_999_title}")
                            log.info("  原因: 标题不包含合同关键词")
                            log.info("  已添加到黑名单，下次运行将自动跳过")
                            log.info("=" * 60)
                            return  # 跳过整个文档的处理
                        else:
                            processed_documents_999_title += 1
                            log.info(
                                f"公告类型999：标题内容包含合同关键词，继续分析: {source_title}"
                            )

                    # 1. 首先分析公告主体内容
                    log.info(f"ID: {doc['_id']} 开始分析公告主体")
                    log.info(f"公告标题: {doc['_source']['title']}")
                    log.info(f"公告链接: {doc['_source']['url']}")
                    log.info(f"公告类型: {doc['_source']['category']}")
                    main_analysis_results = self.analyze_document(doc)

                    # 处理主体解析结果，添加元数据
                    processed_main_results = []
                    for res in main_analysis_results:
                        # 这里res可能是list（如[{},{}]），要遍历
                        if isinstance(res, list):
                            for item in res:
                                item.update(
                                    {
                                        "source_id": doc["_id"],
                                        "source_title": doc["_source"].get("title"),
                                        "source_create_time": doc["_source"].get(
                                            "create_time"
                                        ),
                                        "source_category": doc["_source"].get(
                                            "category"
                                        ),
                                        "source_url": doc["_source"].get("url"),
                                        "source_appendix": doc["_source"].get(
                                            "appendix"
                                        ),
                                        "appendix_info": [],
                                        "bid_doc_name": None,
                                        "bid_doc_ext": None,
                                        "bid_doc_link_out": None,
                                        "bid_doc_link_key": None,
                                        "contract_name": None,
                                        "contract_ext": None,
                                        "contract_link_out": None,
                                        "contract_link_key": None,
                                        "insert_time": datetime.now().strftime(
                                            "%Y-%m-%d %H:%M:%S"
                                        ),
                                    }
                                )
                                processed_main_results.append(item)
                        else:
                            res.update(
                                {
                                    "source_id": doc["_id"],
                                    "source_title": doc["_source"].get("title"),
                                    "source_create_time": doc["_source"].get(
                                        "create_time"
                                    ),
                                    "source_category": doc["_source"].get("category"),
                                    "source_url": doc["_source"].get("url"),
                                    "source_appendix": doc["_source"].get("appendix"),
                                    "appendix_info": [],
                                    "bid_doc_name": None,
                                    "bid_doc_ext": None,
                                    "bid_doc_link_out": None,
                                    "bid_doc_link_key": None,
                                    "contract_name": None,
                                    "contract_ext": None,
                                    "contract_link_out": None,
                                    "contract_link_key": None,
                                    "insert_time": datetime.now().strftime(
                                        "%Y-%m-%d %H:%M:%S"
                                    ),
                                }
                            )
                            processed_main_results.append(res)

                    # 初始化附件分析结果列表和文档内容存储
                    tender_analysis_results = []
                    contract_analysis_results = []
                    tender_content = ""  # 存储招标文件内容
                    contract_content = ""  # 存储合同文件内容

                    # 2. 如果存在附件，则处理所有附件
                    if doc["_source"].get("appendix"):
                        log.info(f"ID: {doc['_id']} 发现附件，开始处理附件")
                        log.info(f"附件列表: {doc['_source']['appendix']}")

                        # Phase 1: 下载并上传所有附件，获取appendix_info、文件内容缓存和文件类型缓存
                        log.info("Phase 1: 开始下载和上传所有附件")
                        appendix_info, file_content_cache, file_type_cache = (
                            process_all_attachments(
                                appendix_list=doc["_source"]["appendix"],
                                source_id=doc["_id"],
                                enable_file_upload=self.enable_file_upload,
                            )
                        )

                        # 更新所有processed_main_results中的appendix_info字段
                        for result in processed_main_results:
                            result["appendix_info"] = appendix_info
                        log.info(f"Phase 1 完成: 处理了 {len(appendix_info)} 个附件")

                        # Phase 2: 分析文件类型，应用早期退出逻辑
                        log.info("Phase 2: 开始分析文件类型")
                        found_tender_file = False
                        found_contract_file = False

                        # 统计变量：用于记录公告类型999的附件过滤情况
                        skipped_attachments_999 = 0
                        total_attachments_999 = 0

                        # 使用缓存的文件内容进行类型检测和内容分析
                        for appendix_item in doc["_source"]["appendix"]:
                            appendix_text = appendix_item.get("text", "")
                            appendix_url = appendix_item.get("url")

                            # 从缓存中获取文件内容，避免重复下载
                            file_content = file_content_cache.get(appendix_url)
                            if not file_content:
                                log.warning(
                                    f"缓存中未找到文件内容，跳过: {appendix_url}"
                                )
                                continue

                            # Phase 2: 直接分析文件内容，不进行文件类型检查
                            # 文件已经下载，直接尝试解析，让解析函数自己判断文件类型
                            log.info(f"开始分析文件内容: {appendix_text}")

                            # 用于日志显示的文件名（不需要扩展名）
                            filename_to_save = appendix_text

                            files_to_analyze = []

                            # 从缓存中获取文件类型信息，避免重复检测
                            file_info = file_type_cache.get(appendix_url)
                            if file_info:
                                detected_ext = file_info.get("ext", "")
                                log.info(
                                    f"从缓存获取文件类型: {detected_ext} for {appendix_text}"
                                )
                            else:
                                # 如果缓存中没有，才进行检测（兜底方案）
                                log.info(
                                    f"缓存中未找到文件类型，重新检测: {appendix_text}"
                                )
                                file_info = get_file_info_from_content(file_content)
                                detected_ext = (
                                    file_info.get("ext", "") if file_info else ""
                                )
                                log.info(
                                    f"重新检测到文件类型: {detected_ext} for {appendix_text}"
                                )

                            # 如果是压缩包，解压并获取内容
                            compressed_ext = [".zip", ".rar", ".7z"]
                            if detected_ext in compressed_ext:
                                log.info(f"发现压缩文件，正在解压: {appendix_url}")
                                original_filename = os.path.basename(
                                    urlparse(appendix_url).path
                                )
                                files_to_analyze = handle_compressed_file(
                                    file_content, original_filename
                                )
                            # 否则，作为单个文件处理
                            else:
                                # 优先使用markitdown进行转换
                                if (
                                    detected_ext in [".pdf", ".docx", ".doc"]
                                    and MARKITDOWN_AVAILABLE
                                ):
                                    log.info(
                                        f"使用markitdown转换文件: {filename_to_save}"
                                    )
                                    markdown_content = (
                                        convert_to_markdown_with_markitdown(
                                            file_content, detected_ext
                                        )
                                    )
                                else:
                                    # 回退到传统解析方法或尝试多种解析方法
                                    log.info(
                                        f"使用传统方法解析文件: {filename_to_save}"
                                    )
                                    parsed_content = ""
                                    if detected_ext == ".pdf":
                                        parsed_content = parse_pdf(file_content)
                                    elif detected_ext == ".docx":
                                        parsed_content = parse_docx(file_content)
                                    elif detected_ext == ".doc":
                                        parsed_content = parse_doc(file_content)
                                    else:
                                        # 如果检测不到类型，尝试所有解析方法
                                        log.info(
                                            f"未知文件类型，尝试多种解析方法: {appendix_text}"
                                        )
                                        for parse_func, ext_name in [
                                            (parse_pdf, "PDF"),
                                            (parse_docx, "DOCX"),
                                            (parse_doc, "DOC"),
                                        ]:
                                            try:
                                                parsed_content = parse_func(
                                                    file_content
                                                )
                                                if (
                                                    parsed_content
                                                    and len(parsed_content.strip()) > 0
                                                ):
                                                    log.info(
                                                        f"成功使用{ext_name}解析器解析文件"
                                                    )
                                                    break
                                            except Exception as e:
                                                log.debug(f"{ext_name}解析失败: {e}")
                                                continue

                                    # 如果解析出内容，转换为简单的markdown格式
                                    if parsed_content:
                                        # 简单的文本到markdown转换，保持原有格式
                                        markdown_content = parsed_content
                                    else:
                                        markdown_content = ""

                                if markdown_content:
                                    files_to_analyze.append(
                                        {
                                            "filename": os.path.basename(
                                                urlparse(appendix_url).path
                                            ),
                                            "content": markdown_content,
                                            "file_ext": detected_ext,
                                            "file_bytes": file_content,
                                        }
                                    )

                            # 对所有待分析文件进行LLM分析
                            for item in files_to_analyze:
                                log.info(f"正在分析文件: {item['filename']}")
                                # # 新增：提取预览文本并判断类型
                                # preview = extract_preview_text(
                                #     item.get("file_ext", ""),
                                #     item.get("file_bytes", b""),
                                # )
                                # 暂时不对预览文件进行类型判断
                                preview = ""
                                file_type = detect_file_type(appendix_text, preview)

                                # 早期退出机制：如果已经找到对应类型的文件，跳过后续同类型文件
                                if file_type == "招标文件" and found_tender_file:
                                    log.info(
                                        f"已找到招标文件，跳过后续招标文件: {item['filename']}"
                                    )
                                    continue
                                elif file_type == "合同文件" and found_contract_file:
                                    log.info(
                                        f"已找到合同文件，跳过后续合同文件: {item['filename']}"
                                    )
                                    continue
                                elif file_type == "其他":
                                    log.info(f"跳过非招标/合同文件: {item['filename']}")
                                    continue

                                # 存储文档内容用于智能融合
                                if file_type == "招标文件":
                                    tender_content = item["content"]
                                    log.info(f"存储招标文件内容: {item['filename']}")
                                elif file_type == "合同文件":
                                    contract_content = item["content"]
                                    log.info(f"存储合同文件内容: {item['filename']}")

                                # 更新特定的招标/合同文件字段
                                if file_type == "招标文件":
                                    # 从appendix_info中找到对应的文件信息
                                    matching_attachment = None
                                    for attachment in appendix_info:
                                        if attachment["url"] == appendix_url:
                                            matching_attachment = attachment
                                            break

                                    if matching_attachment:
                                        # 更新所有processed_main_results中的招标文件字段
                                        for result in processed_main_results:
                                            result["bid_doc_name"] = (
                                                appendix_text  # 使用原始文件名
                                            )
                                            result["bid_doc_ext"] = detected_ext
                                            result["bid_doc_link_out"] = appendix_url
                                            result["bid_doc_link_key"] = (
                                                matching_attachment.get("file_link_key")
                                            )
                                        log.info(f"已更新招标文件字段: {appendix_text}")
                                        found_tender_file = (
                                            True  # 设置标志，后续跳过招标文件
                                        )

                                elif file_type == "合同文件":
                                    # 从appendix_info中找到对应的文件信息
                                    matching_attachment = None
                                    for attachment in appendix_info:
                                        if attachment["url"] == appendix_url:
                                            matching_attachment = attachment
                                            break

                                    if matching_attachment:
                                        # 更新所有processed_main_results中的合同文件字段
                                        for result in processed_main_results:
                                            result["contract_name"] = (
                                                appendix_text  # 使用原始文件名
                                            )
                                            result["contract_ext"] = detected_ext
                                            result["contract_link_out"] = appendix_url
                                            result["contract_link_key"] = (
                                                matching_attachment.get("file_link_key")
                                            )
                                        log.info(f"已更新合同文件字段: {appendix_text}")
                                        found_contract_file = (
                                            True  # 设置标志，后续跳过合同文件
                                        )

                                # 针对公告类型999的附件内容过滤逻辑
                                # category = doc["_source"].get("category")

                                if "999" == category:
                                    total_attachments_999 += 1
                                    # 检查附件文本内容是否包含合同关键词
                                    if not contains_contract_keywords(item["content"]):
                                        skipped_attachments_999 += 1
                                        log.info(
                                            f"公告类型999：附件内容不包含合同关键词，跳过分析: {item['filename']}"
                                        )
                                        log.debug(
                                            f"附件内容预览: {item['content'][:200]}..."
                                        )
                                        continue
                                    else:
                                        log.info(
                                            f"公告类型999：附件内容包含合同关键词，继续分析: {item['filename']}"
                                        )

                                try:
                                    analysis_results = self.analyze_content(
                                        item["content"], doc["_source"]["title"]
                                    )
                                except Exception as e:
                                    log.error(f"文档解析失败: {e}")
                                    log.warning(
                                        f"跳过文档解析，继续处理其他附件: {item['filename']}"
                                    )
                                    # 跳过这个文件的解析，但不中断整个流程
                                    continue
                                for res in analysis_results:
                                    # 如果 res 还是 list，继续展开
                                    if isinstance(res, list):
                                        for sub_res in res:
                                            update_data = {
                                                "source_id": doc["_id"],
                                                "source_title": doc["_source"].get(
                                                    "title"
                                                ),
                                                "source_create_time": doc[
                                                    "_source"
                                                ].get("create_time"),
                                                "source_category": doc["_source"].get(
                                                    "category"
                                                ),
                                                "source_url": doc["_source"].get("url"),
                                                "source_appendix": doc["_source"].get(
                                                    "appendix"
                                                ),
                                                "insert_time": datetime.now().strftime(
                                                    "%Y-%m-%d %H:%M:%S"
                                                ),
                                                "bid_doc_name": None,
                                                "bid_doc_ext": None,
                                                "bid_doc_link_out": None,
                                                "bid_doc_link_key": None,
                                                "contract_name": None,
                                                "contract_ext": None,
                                                "contract_link_out": None,
                                                "contract_link_key": None,
                                            }
                                            # 注意：招标/合同文件字段已在Phase 2中直接更新到processed_main_results
                                            # 这里不再重复设置这些字段

                                            sub_res.update(update_data)
                                            # 只添加到对应的分析结果列表中，不添加到all_analyzed_docs
                                            if file_type == "招标文件":
                                                tender_analysis_results.append(sub_res)
                                                log.info(
                                                    f"已找到并分析招标文件: {item['filename']}"
                                                )
                                            elif file_type == "合同文件":
                                                contract_analysis_results.append(
                                                    sub_res
                                                )
                                                log.info(
                                                    f"已找到并分析合同文件: {item['filename']}"
                                                )
                                    else:
                                        update_data = {
                                            "source_id": doc["_id"],
                                            "source_title": doc["_source"].get("title"),
                                            "source_create_time": doc["_source"].get(
                                                "create_time"
                                            ),
                                            "source_category": doc["_source"].get(
                                                "category"
                                            ),
                                            "source_url": doc["_source"].get("url"),
                                            "source_appendix": doc["_source"].get(
                                                "appendix"
                                            ),
                                            "insert_time": datetime.now().strftime(
                                                "%Y-%m-%d %H:%M:%S"
                                            ),
                                            "bid_doc_name": None,
                                            "bid_doc_ext": None,
                                            "bid_doc_link_out": None,
                                            "bid_doc_link_key": None,
                                            "contract_name": None,
                                            "contract_ext": None,
                                            "contract_link_out": None,
                                            "contract_link_key": None,
                                        }
                                        # 注意：招标/合同文件字段已在Phase 2中直接更新到processed_main_results
                                        # 这里不再重复设置这些字段

                                        res.update(update_data)
                                        # 只添加到对应的分析结果列表中，不添加到all_analyzed_docs
                                        if file_type == "招标文件":
                                            tender_analysis_results.append(res)
                                            log.info(
                                                f"已找到并分析招标文件: {item['filename']}"
                                            )
                                        elif file_type == "合同文件":
                                            contract_analysis_results.append(res)
                                            log.info(
                                                f"已找到并分析合同文件: {item['filename']}"
                                            )

                            # 可选的完全早期退出：如果两种文件都已找到，可以提前结束附件遍历
                            # 注释掉以下代码块如果需要处理所有附件
                            if found_tender_file and found_contract_file:
                                log.info(
                                    "招标文件和合同文件都已找到，提前结束附件遍历以提高效率"
                                )
                                break

                        # 输出公告类型999的附件过滤统计信息
                        if "999" == category and total_attachments_999 > 0:
                            log.info("=" * 60)
                            log.info("公告类型999附件过滤统计:")
                            log.info(f"  总附件数: {total_attachments_999}")
                            log.info(f"  跳过的附件数: {skipped_attachments_999}")
                            log.info(
                                f"  分析的附件数: {total_attachments_999 - skipped_attachments_999}"
                            )
                            log.info(
                                f"  跳过率: {skipped_attachments_999/total_attachments_999*100:.1f}%"
                            )
                            log.info("=" * 60)

                    # 融合分析结果
                    main_result_list = ensure_list(processed_main_results)
                    tender_result_list = ensure_list(tender_analysis_results)
                    contract_result_list = ensure_list(contract_analysis_results)

                    # 打印融合前的各部分内容
                    log.info("=" * 80)
                    log.info("开始融合分析结果")
                    log.info("=" * 80)
                    log.info(f"主体解析结果数量: {len(main_result_list)}")
                    log.info(f"招标文件解析结果数量: {len(tender_result_list)}")
                    log.info(f"合同文件解析结果数量: {len(contract_result_list)}")

                    if main_result_list:
                        log.info("主体解析结果:")
                        for i, main_result in enumerate(main_result_list):
                            log.info(f"主体结果 {i+1}:")
                            log.info(
                                json.dumps(main_result, ensure_ascii=False, indent=2)
                            )

                    if tender_result_list:
                        log.info("招标文件解析结果:")
                        for i, tender_result in enumerate(tender_result_list):
                            log.info(f"招标文件结果 {i+1}:")
                            log.info(
                                json.dumps(tender_result, ensure_ascii=False, indent=2)
                            )

                    if contract_result_list:
                        log.info("合同文件解析结果:")
                        for i, contract_result in enumerate(contract_result_list):
                            log.info(f"合同文件结果 {i+1}:")
                            log.info(
                                json.dumps(
                                    contract_result, ensure_ascii=False, indent=2
                                )
                            )

                    # 总是执行智能融合分析，即使主体解析结果为空
                    # 这样可以在主体解析失败时使用招标文件或合同文件的解析结果
                    log.info("使用智能融合分析")
                    final_results = intelligent_merge_analysis(
                        main_list=main_result_list,
                        tender_content=tender_content,
                        contract_content=contract_content,
                        tender_list=tender_result_list,
                        contract_list=contract_result_list,
                        model_apikey=self.model_apikey,
                        model_name=self.model_name,
                        model_url=self.model_url,
                        timeout=self.timeout,
                        max_retries=self.max_retries,
                    )

                    # 打印最终融合后的JSON列表
                    log.info("=" * 80)
                    log.info("最终融合后的JSON结果列表:")
                    log.info("=" * 80)
                    for i, final_result in enumerate(final_results):
                        log.info(f"融合结果 {i+1}:")
                        log.info(json.dumps(final_result, ensure_ascii=False, indent=2))
                        log.info("-" * 60)
                    log.info("=" * 80)

                    # 然后插入ES或后续处理
                    for i, final_result in enumerate(final_results):
                        new_doc_id = f"{final_result['source_id']}_{i}"

                        # 获取公告类型用于字段校验
                        announcement_type = final_result.get("source_category")

                        # 字段校验和标准化
                        validated_document = validate_and_normalize_fields(
                            final_result, announcement_type=announcement_type
                        )

                        insert_document(
                            self.es,
                            self.es_index_analysis,
                            doc_id=new_doc_id,
                            document=validated_document,
                        )
                        log.info(f"成功插入融合文档 {new_doc_id}")

                else:
                    log.info("未找到未处理的数据1")
            else:
                log.info("未找到未处理的数据2")

            # # 输出公告类型999的标题过滤最终统计信息
            # if "999" in categories and (
            #     skipped_documents_999_title > 0 or processed_documents_999_title > 0
            # ):
            #     log.info("=" * 60)
            #     log.info("公告类型999标题过滤最终统计:")
            #     log.info(f"  处理的文档数: {processed_documents_999_title}")
            #     log.info(f"  跳过的文档数: {skipped_documents_999_title}")
            #     total_checked = (
            #         processed_documents_999_title + skipped_documents_999_title
            #     )
            #     if total_checked > 0:
            #         skip_rate = (skipped_documents_999_title / total_checked) * 100
            #         log.info(f"  跳过率: {skip_rate:.1f}%")
            #     log.info("=" * 60)

        except Exception as e:
            log.error(f"处理过程中发生异常: {e}")

            # 检查是否是LLM调用失败（已重试2次）
            error_msg = str(e)
            if "LLM API调用失败" in error_msg and "已重试2次" in error_msg:
                log.warning(
                    f"LLM调用失败达到最大重试次数，将文档 {doc['_id']} 添加到黑名单"
                )

                # 添加到黑名单
                doc_title = doc.get("_source", {}).get("title", "无标题")
                doc_url = doc.get("_source", {}).get("url", "")

                success = self.blacklist_manager.add_to_blacklist(
                    document_id=doc["_id"],
                    document_title=doc_title,
                    document_url=doc_url,
                    failure_reason=f"LLM API调用失败: {error_msg[:200]}...",
                )

                if success:
                    log.info(f"✓ 文档 {doc['_id']} 已添加到黑名单，下次运行将跳过")
                else:
                    log.error(f"✗ 添加文档 {doc['_id']} 到黑名单失败")

            log.warning("尝试保存已处理的主体解析结果...")

            # 尝试保存主体解析结果，即使附件处理失败
            try:
                # 检查main_result_list变量是否已定义
                if "main_result_list" in locals() and main_result_list:
                    log.info(f"保存{len(main_result_list)}个主体解析结果")
                    for i, result in enumerate(main_result_list):
                        new_doc_id = f"{doc['_id']}_main_{i}"

                        # 获取公告类型用于字段校验
                        announcement_type = result.get("source_category")

                        # 字段校验和标准化
                        validated_document = validate_and_normalize_fields(
                            result, announcement_type=announcement_type
                        )

                        insert_document(
                            self.es,
                            self.es_index_analysis,
                            doc_id=new_doc_id,
                            document=validated_document,
                        )
                        log.info(f"成功插入主体解析结果 {new_doc_id}")
                else:
                    log.warning("没有主体解析结果可保存")
            except Exception as save_error:
                log.error(f"保存主体解析结果也失败: {save_error}")
                raise Exception(f"获取公告链接数据失败: {e}")


def get_one_record():
    try:
        # 初始化ES客户端
        es = init_es_client()

        # 加载.env文件
        load_dotenv()

        # 从环境变量获取配置
        es_index_links = os.getenv("ES_INDEX_LINKS")
        es_index_analysis = os.getenv("ES_INDEX_ANALYSIS_ALIAS")
        model_apikey = os.getenv("MODEL_APIKEY")
        model_name = os.getenv("MODEL_NAME")
        model_url = os.getenv("MODEL_URL")
        prompt_spec = os.getenv(
            "PROMPT_SPEC", ""
        )  # 从环境变量获取prompt_spec，如果没有则使用空字符串
        enable_file_upload = (
            os.getenv("ENABLE_FILE_UPLOAD", "true").lower() == "true"
        )  # 从环境变量获取文件上传开关

        # 创建分析器实例
        analyzer = DocumentAnalyzer(
            es_client=es,
            es_index_links=es_index_links,
            es_index_analysis=es_index_analysis,
            model_apikey=model_apikey,
            model_name=model_name,  # 新增参数model_url和model_name，用于指定模型名称和URL。
            model_url=model_url,  # 新增参数model_url和model_name，用于指定模型名称和URL。
            prompt_spec=prompt_spec,  # 添加缺少的prompt_spec参数
            enable_file_upload=enable_file_upload,  # 添加文件上传开关参数
        )

        # 处理一条记录
        analyzer.process_one_record()

    except Exception as e:
        raise Exception(f"处理失败: {e}")


def manage_blacklist():
    """黑名单管理功能"""
    import argparse
    import sys

    # 处理以-开头的文档ID的特殊情况
    # 如果命令行参数中有以-开头但不是已知选项的参数，将其视为文档ID
    processed_args = []
    i = 0
    while i < len(sys.argv):
        arg = sys.argv[i]
        if arg.startswith("-") and not arg.startswith("--") and len(arg) > 2:
            # 检查是否是文档ID（不是已知的短选项）
            if i > 0 and sys.argv[i - 1] == "--id":
                # 这是--id参数的值，保持原样
                processed_args.append(arg)
            elif arg not in ["-h"]:
                # 可能是文档ID，需要特殊处理
                if i > 0 and sys.argv[i - 1] == "--id":
                    processed_args.append(arg)
                else:
                    processed_args.append(arg)
            else:
                processed_args.append(arg)
        else:
            processed_args.append(arg)
        i += 1

    parser = argparse.ArgumentParser(description="黑名单管理工具")
    parser.add_argument(
        "--action",
        choices=["list", "stats", "clear", "remove", "add"],
        required=True,
        help="操作类型",
    )
    parser.add_argument(
        "--id",
        metavar="DOCUMENT_ID",
        help='文档ID（用于add和remove操作）。对于以-开头的ID，请使用：--id="-1o4r5cBsUtJ06NfkR31" 格式',
    )
    parser.add_argument("--title", help="文档标题（用于add操作，可选）")
    parser.add_argument("--url", help="文档URL（用于add操作，可选）")
    parser.add_argument("--reason", help="失败原因（用于add操作，可选）")
    parser.add_argument(
        "--limit", type=int, default=20, help="显示数量限制（仅用于list操作）"
    )

    try:
        args = parser.parse_args()
    except SystemExit as e:
        if e.code != 0:
            print("\n提示：如果文档ID以-开头，请使用以下格式之一：")
            print('  --id="-1o4r5cBsUtJ06NfkR31"')
            print('  或在PowerShell中使用：--id `"-1o4r5cBsUtJ06NfkR31`"')
        raise

    blacklist_manager = BlacklistManager()

    if args.action == "stats":
        print_blacklist_stats(blacklist_manager)

    elif args.action == "list":
        blacklist = blacklist_manager.get_blacklist(limit=args.limit)
        if blacklist:
            print(f"\n黑名单文档列表 (显示前{len(blacklist)}条):")
            print("=" * 100)
            for i, item in enumerate(blacklist, 1):
                print(f"{i}. ID: {item['document_id']}")
                print(f"   标题: {item['document_title'] or '无标题'}")
                print(f"   失败次数: {item['failure_count']}")
                print(f"   最后失败时间: {item['last_failure_time']}")
                print(f"   失败原因: {item['failure_reason'][:100]}...")
                print("-" * 100)
        else:
            print("黑名单为空")

    elif args.action == "clear":
        confirm = input("确认要清空整个黑名单吗？(y/N): ")
        if confirm.lower() == "y":
            if blacklist_manager.clear_blacklist():
                print("✓ 黑名单已清空")
            else:
                print("✗ 清空黑名单失败")
        else:
            print("操作已取消")

    elif args.action == "add":
        if not args.id:
            print("错误：add操作需要指定--id参数")
            return

        # 检查文档是否已在黑名单中
        if blacklist_manager.is_blacklisted(args.id):
            print(f"警告：文档 {args.id} 已在黑名单中")
            confirm = input("是否要更新该文档的信息？(y/N): ")
            if confirm.lower() != "y":
                print("操作已取消")
                return

        # 添加到黑名单
        success = blacklist_manager.add_to_blacklist(
            document_id=args.id,
            document_title=args.title or "手动添加",
            document_url=args.url or "",
            failure_reason=args.reason or "手动添加到黑名单",
        )

        if success:
            print(f"✓ 已将文档 {args.id} 添加到黑名单")
            if args.title:
                print(f"  标题: {args.title}")
            if args.url:
                print(f"  URL: {args.url}")
            if args.reason:
                print(f"  原因: {args.reason}")
        else:
            print(f"✗ 添加文档 {args.id} 到黑名单失败")

    elif args.action == "remove":
        if not args.id:
            print("错误：remove操作需要指定--id参数")
            return

        if blacklist_manager.remove_from_blacklist(args.id):
            print(f"✓ 已从黑名单中移除文档: {args.id}")
        else:
            print(f"✗ 移除失败，文档可能不在黑名单中: {args.id}")


if __name__ == "__main__":
    import sys

    # 检查是否是黑名单管理模式
    if len(sys.argv) > 1 and sys.argv[1] == "blacklist":
        # 移除第一个参数，让argparse正确解析
        sys.argv = [sys.argv[0]] + sys.argv[2:]
        manage_blacklist()
    else:
        get_one_record()
